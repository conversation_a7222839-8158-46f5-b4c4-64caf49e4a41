package scheduler

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/ccron"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/inv"
	"github.com/pkg/errors"
)

type SchedulerService interface {
}

type service struct {
	cfg          *modcfg.MainConfig
	invService   inv.InvService
	ccronService ccron.Service
	repo         repo.Repository
}

func NewService(cfg *modcfg.MainConfig, invService inv.InvService, repo repo.Repository) (SchedulerService, error) {
	if cs, err := ccron.NewScheduler(); err != nil {
		return nil, errors.Wrap(err, "initialising cron job scheduler failed")
	} else if err = initialiseScheduledJobs(cfg, cs, invService, repo); err != nil {
		return nil, err
	} else {
		return &service{
			cfg:          cfg,
			invService:   invService,
			ccronService: cs,
			repo:         repo,
		}, nil
	}
}

func initialiseScheduledJobs(cfg *modcfg.MainConfig, cs ccron.Service, invService inv.InvService, repo repo.Repository) error {
	for _, scheduler := range cfg.Schedulers {
		var err error
		switch scheduler.Name {
		case "inventory":
			err = cs.AddProcessor(scheduler, newInventoryJobProcessor(scheduler, invService))
		case "warningDormancy":
			err = cs.AddProcessor(scheduler, newWarningDormancyJobProcessor(*cfg, scheduler, repo))
		case "users":
			var usersJobProcessor *ccron.Processor
			usersJobProcessor, err = newUsersJobProcessor(*cfg, scheduler, repo)
			if err != nil {
				return err
			}
			err = cs.AddProcessor(scheduler, *usersJobProcessor)
		default:
			return errors.Errorf("invalid scheduled job category: %s", scheduler.Name)
		}
		if err != nil {
			return errors.Wrapf(err, "initialising schedule job processor for %s failed", scheduler.Name)
		}
	}

	return cs.Start()
}
