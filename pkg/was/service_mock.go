// Code generated by mockery. DO NOT EDIT.

//
//go:build !compile

package was

import (
	authmod "github.com/CyberOwlTeam/go-authentication/pkg/authmod"
	calc "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/calc"

	cjson "github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"

	inv "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/inv"

	invmod "github.com/CyberOwlTeam/go-inventory-client/pkg/invmod"

	json "encoding/json"

	kcmodel "github.com/CyberOwlTeam/go-kafka-client/pkg/kcmodel"

	kcproc "github.com/CyberOwlTeam/go-kafka-client/pkg/kcproc"

	metrics "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/metrics"

	mock "github.com/stretchr/testify/mock"

	modbus "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modbus"

	modcalc "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcalc"

	modinv "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"

	modnotif "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"

	modusr "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"

	modwarn "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"

	repo "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"

	scheduler "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/scheduler"

	time "time"

	usr "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/usr"

	uuid "github.com/google/uuid"

	warning "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/warning"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

type MockService_Expecter struct {
	mock *mock.Mock
}

func (_m *MockService) EXPECT() *MockService_Expecter {
	return &MockService_Expecter{mock: &_m.Mock}
}

// CalculateLocationScores provides a mock function with given fields: criteria
func (_m *MockService) CalculateLocationScores(criteria modcalc.Criteria) (map[uuid.UUID][]modcalc.CalculationPerCategory, error) {
	ret := _m.Called(criteria)

	if len(ret) == 0 {
		panic("no return value specified for CalculateLocationScores")
	}

	var r0 map[uuid.UUID][]modcalc.CalculationPerCategory
	var r1 error
	if rf, ok := ret.Get(0).(func(modcalc.Criteria) (map[uuid.UUID][]modcalc.CalculationPerCategory, error)); ok {
		return rf(criteria)
	}
	if rf, ok := ret.Get(0).(func(modcalc.Criteria) map[uuid.UUID][]modcalc.CalculationPerCategory); ok {
		r0 = rf(criteria)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[uuid.UUID][]modcalc.CalculationPerCategory)
		}
	}

	if rf, ok := ret.Get(1).(func(modcalc.Criteria) error); ok {
		r1 = rf(criteria)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CalculateLocationScores_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CalculateLocationScores'
type MockService_CalculateLocationScores_Call struct {
	*mock.Call
}

// CalculateLocationScores is a helper method to define mock.On call
//   - criteria modcalc.Criteria
func (_e *MockService_Expecter) CalculateLocationScores(criteria interface{}) *MockService_CalculateLocationScores_Call {
	return &MockService_CalculateLocationScores_Call{Call: _e.mock.On("CalculateLocationScores", criteria)}
}

func (_c *MockService_CalculateLocationScores_Call) Run(run func(criteria modcalc.Criteria)) *MockService_CalculateLocationScores_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modcalc.Criteria))
	})
	return _c
}

func (_c *MockService_CalculateLocationScores_Call) Return(_a0 map[uuid.UUID][]modcalc.CalculationPerCategory, _a1 error) *MockService_CalculateLocationScores_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CalculateLocationScores_Call) RunAndReturn(run func(modcalc.Criteria) (map[uuid.UUID][]modcalc.CalculationPerCategory, error)) *MockService_CalculateLocationScores_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAffectedItem provides a mock function with given fields: item
func (_m *MockService) CreateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	ret := _m.Called(item)

	if len(ret) == 0 {
		panic("no return value specified for CreateAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)); ok {
		return rf(item)
	}
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) *modnotif.AffectedItem); ok {
		r0 = rf(item)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.AffectedItem) error); ok {
		r1 = rf(item)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAffectedItem'
type MockService_CreateAffectedItem_Call struct {
	*mock.Call
}

// CreateAffectedItem is a helper method to define mock.On call
//   - item modnotif.AffectedItem
func (_e *MockService_Expecter) CreateAffectedItem(item interface{}) *MockService_CreateAffectedItem_Call {
	return &MockService_CreateAffectedItem_Call{Call: _e.mock.On("CreateAffectedItem", item)}
}

func (_c *MockService_CreateAffectedItem_Call) Run(run func(item modnotif.AffectedItem)) *MockService_CreateAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.AffectedItem))
	})
	return _c
}

func (_c *MockService_CreateAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockService_CreateAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateAffectedItem_Call) RunAndReturn(run func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)) *MockService_CreateAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAsset provides a mock function with given fields: asset
func (_m *MockService) CreateAsset(asset modinv.Asset) (*modinv.Asset, error) {
	ret := _m.Called(asset)

	if len(ret) == 0 {
		panic("no return value specified for CreateAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Asset) (*modinv.Asset, error)); ok {
		return rf(asset)
	}
	if rf, ok := ret.Get(0).(func(modinv.Asset) *modinv.Asset); ok {
		r0 = rf(asset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Asset) error); ok {
		r1 = rf(asset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAsset'
type MockService_CreateAsset_Call struct {
	*mock.Call
}

// CreateAsset is a helper method to define mock.On call
//   - asset modinv.Asset
func (_e *MockService_Expecter) CreateAsset(asset interface{}) *MockService_CreateAsset_Call {
	return &MockService_CreateAsset_Call{Call: _e.mock.On("CreateAsset", asset)}
}

func (_c *MockService_CreateAsset_Call) Run(run func(asset modinv.Asset)) *MockService_CreateAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Asset))
	})
	return _c
}

func (_c *MockService_CreateAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockService_CreateAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateAsset_Call) RunAndReturn(run func(modinv.Asset) (*modinv.Asset, error)) *MockService_CreateAsset_Call {
	_c.Call.Return(run)
	return _c
}

// CreateLocation provides a mock function with given fields: location
func (_m *MockService) CreateLocation(location modinv.Location) (*modinv.Location, error) {
	ret := _m.Called(location)

	if len(ret) == 0 {
		panic("no return value specified for CreateLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Location) (*modinv.Location, error)); ok {
		return rf(location)
	}
	if rf, ok := ret.Get(0).(func(modinv.Location) *modinv.Location); ok {
		r0 = rf(location)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Location) error); ok {
		r1 = rf(location)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateLocation'
type MockService_CreateLocation_Call struct {
	*mock.Call
}

// CreateLocation is a helper method to define mock.On call
//   - location modinv.Location
func (_e *MockService_Expecter) CreateLocation(location interface{}) *MockService_CreateLocation_Call {
	return &MockService_CreateLocation_Call{Call: _e.mock.On("CreateLocation", location)}
}

func (_c *MockService_CreateLocation_Call) Run(run func(location modinv.Location)) *MockService_CreateLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Location))
	})
	return _c
}

func (_c *MockService_CreateLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockService_CreateLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateLocation_Call) RunAndReturn(run func(modinv.Location) (*modinv.Location, error)) *MockService_CreateLocation_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationRule provides a mock function with given fields: rule
func (_m *MockService) CreateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	ret := _m.Called(rule)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)); ok {
		return rf(rule)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) *modnotif.NotificationRule); ok {
		r0 = rf(rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationRule) error); ok {
		r1 = rf(rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationRule'
type MockService_CreateNotificationRule_Call struct {
	*mock.Call
}

// CreateNotificationRule is a helper method to define mock.On call
//   - rule modnotif.NotificationRule
func (_e *MockService_Expecter) CreateNotificationRule(rule interface{}) *MockService_CreateNotificationRule_Call {
	return &MockService_CreateNotificationRule_Call{Call: _e.mock.On("CreateNotificationRule", rule)}
}

func (_c *MockService_CreateNotificationRule_Call) Run(run func(rule modnotif.NotificationRule)) *MockService_CreateNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationRule))
	})
	return _c
}

func (_c *MockService_CreateNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockService_CreateNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateNotificationRule_Call) RunAndReturn(run func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)) *MockService_CreateNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationSetting provides a mock function with given fields: setting
func (_m *MockService) CreateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(setting)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)); ok {
		return rf(setting)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) *modnotif.NotificationSetting); ok {
		r0 = rf(setting)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationSetting) error); ok {
		r1 = rf(setting)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationSetting'
type MockService_CreateNotificationSetting_Call struct {
	*mock.Call
}

// CreateNotificationSetting is a helper method to define mock.On call
//   - setting modnotif.NotificationSetting
func (_e *MockService_Expecter) CreateNotificationSetting(setting interface{}) *MockService_CreateNotificationSetting_Call {
	return &MockService_CreateNotificationSetting_Call{Call: _e.mock.On("CreateNotificationSetting", setting)}
}

func (_c *MockService_CreateNotificationSetting_Call) Run(run func(setting modnotif.NotificationSetting)) *MockService_CreateNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationSetting))
	})
	return _c
}

func (_c *MockService_CreateNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockService_CreateNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateNotificationSetting_Call) RunAndReturn(run func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)) *MockService_CreateNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUser provides a mock function with given fields: user
func (_m *MockService) CreateUser(user modusr.User) (*modusr.User, error) {
	ret := _m.Called(user)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(modusr.User) (*modusr.User, error)); ok {
		return rf(user)
	}
	if rf, ok := ret.Get(0).(func(modusr.User) *modusr.User); ok {
		r0 = rf(user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(modusr.User) error); ok {
		r1 = rf(user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type MockService_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - user modusr.User
func (_e *MockService_Expecter) CreateUser(user interface{}) *MockService_CreateUser_Call {
	return &MockService_CreateUser_Call{Call: _e.mock.On("CreateUser", user)}
}

func (_c *MockService_CreateUser_Call) Run(run func(user modusr.User)) *MockService_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modusr.User))
	})
	return _c
}

func (_c *MockService_CreateUser_Call) Return(_a0 *modusr.User, _a1 error) *MockService_CreateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateUser_Call) RunAndReturn(run func(modusr.User) (*modusr.User, error)) *MockService_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWarning provides a mock function with given fields: _a0
func (_m *MockService) CreateWarning(_a0 modwarn.Warning) (*modwarn.Warning, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for CreateWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.Warning) (*modwarn.Warning, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(modwarn.Warning) *modwarn.Warning); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.Warning) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWarning'
type MockService_CreateWarning_Call struct {
	*mock.Call
}

// CreateWarning is a helper method to define mock.On call
//   - _a0 modwarn.Warning
func (_e *MockService_Expecter) CreateWarning(_a0 interface{}) *MockService_CreateWarning_Call {
	return &MockService_CreateWarning_Call{Call: _e.mock.On("CreateWarning", _a0)}
}

func (_c *MockService_CreateWarning_Call) Run(run func(_a0 modwarn.Warning)) *MockService_CreateWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.Warning))
	})
	return _c
}

func (_c *MockService_CreateWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockService_CreateWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateWarning_Call) RunAndReturn(run func(modwarn.Warning) (*modwarn.Warning, error)) *MockService_CreateWarning_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWarningHistory provides a mock function with given fields: warningHistory
func (_m *MockService) CreateWarningHistory(warningHistory modwarn.WarningHistory) (*modwarn.WarningHistory, error) {
	ret := _m.Called(warningHistory)

	if len(ret) == 0 {
		panic("no return value specified for CreateWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory) error); ok {
		r1 = rf(warningHistory)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_CreateWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWarningHistory'
type MockService_CreateWarningHistory_Call struct {
	*mock.Call
}

// CreateWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
func (_e *MockService_Expecter) CreateWarningHistory(warningHistory interface{}) *MockService_CreateWarningHistory_Call {
	return &MockService_CreateWarningHistory_Call{Call: _e.mock.On("CreateWarningHistory", warningHistory)}
}

func (_c *MockService_CreateWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory)) *MockService_CreateWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningHistory))
	})
	return _c
}

func (_c *MockService_CreateWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockService_CreateWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_CreateWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)) *MockService_CreateWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAffectedItem provides a mock function with given fields: identity
func (_m *MockService) DeleteAffectedItem(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAffectedItem")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAffectedItem'
type MockService_DeleteAffectedItem_Call struct {
	*mock.Call
}

// DeleteAffectedItem is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteAffectedItem(identity interface{}) *MockService_DeleteAffectedItem_Call {
	return &MockService_DeleteAffectedItem_Call{Call: _e.mock.On("DeleteAffectedItem", identity)}
}

func (_c *MockService_DeleteAffectedItem_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteAffectedItem_Call) Return(_a0 bool, _a1 error) *MockService_DeleteAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteAffectedItem_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAsset provides a mock function with given fields: identity
func (_m *MockService) DeleteAsset(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAsset")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAsset'
type MockService_DeleteAsset_Call struct {
	*mock.Call
}

// DeleteAsset is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteAsset(identity interface{}) *MockService_DeleteAsset_Call {
	return &MockService_DeleteAsset_Call{Call: _e.mock.On("DeleteAsset", identity)}
}

func (_c *MockService_DeleteAsset_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteAsset_Call) Return(_a0 bool, _a1 error) *MockService_DeleteAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteAsset_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteAsset_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteLocation provides a mock function with given fields: identity
func (_m *MockService) DeleteLocation(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteLocation")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteLocation'
type MockService_DeleteLocation_Call struct {
	*mock.Call
}

// DeleteLocation is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteLocation(identity interface{}) *MockService_DeleteLocation_Call {
	return &MockService_DeleteLocation_Call{Call: _e.mock.On("DeleteLocation", identity)}
}

func (_c *MockService_DeleteLocation_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteLocation_Call) Return(_a0 bool, _a1 error) *MockService_DeleteLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteLocation_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteLocation_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationRule provides a mock function with given fields: identity
func (_m *MockService) DeleteNotificationRule(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationRule")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationRule'
type MockService_DeleteNotificationRule_Call struct {
	*mock.Call
}

// DeleteNotificationRule is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteNotificationRule(identity interface{}) *MockService_DeleteNotificationRule_Call {
	return &MockService_DeleteNotificationRule_Call{Call: _e.mock.On("DeleteNotificationRule", identity)}
}

func (_c *MockService_DeleteNotificationRule_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteNotificationRule_Call) Return(_a0 bool, _a1 error) *MockService_DeleteNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteNotificationRule_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationSetting provides a mock function with given fields: identity
func (_m *MockService) DeleteNotificationSetting(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationSetting'
type MockService_DeleteNotificationSetting_Call struct {
	*mock.Call
}

// DeleteNotificationSetting is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteNotificationSetting(identity interface{}) *MockService_DeleteNotificationSetting_Call {
	return &MockService_DeleteNotificationSetting_Call{Call: _e.mock.On("DeleteNotificationSetting", identity)}
}

func (_c *MockService_DeleteNotificationSetting_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteNotificationSetting_Call) Return(_a0 bool, _a1 error) *MockService_DeleteNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteNotificationSetting_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: identity
func (_m *MockService) DeleteUser(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockService_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteUser(identity interface{}) *MockService_DeleteUser_Call {
	return &MockService_DeleteUser_Call{Call: _e.mock.On("DeleteUser", identity)}
}

func (_c *MockService_DeleteUser_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteUser_Call) Return(_a0 bool, _a1 error) *MockService_DeleteUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteUser_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarning provides a mock function with given fields: identity
func (_m *MockService) DeleteWarning(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarning")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarning'
type MockService_DeleteWarning_Call struct {
	*mock.Call
}

// DeleteWarning is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteWarning(identity interface{}) *MockService_DeleteWarning_Call {
	return &MockService_DeleteWarning_Call{Call: _e.mock.On("DeleteWarning", identity)}
}

func (_c *MockService_DeleteWarning_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteWarning_Call) Return(_a0 bool, _a1 error) *MockService_DeleteWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteWarning_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteWarning_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarningHistory provides a mock function with given fields: identity
func (_m *MockService) DeleteWarningHistory(identity uuid.UUID) (bool, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarningHistory")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (bool, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) bool); ok {
		r0 = rf(identity)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_DeleteWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarningHistory'
type MockService_DeleteWarningHistory_Call struct {
	*mock.Call
}

// DeleteWarningHistory is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) DeleteWarningHistory(identity interface{}) *MockService_DeleteWarningHistory_Call {
	return &MockService_DeleteWarningHistory_Call{Call: _e.mock.On("DeleteWarningHistory", identity)}
}

func (_c *MockService_DeleteWarningHistory_Call) Run(run func(identity uuid.UUID)) *MockService_DeleteWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_DeleteWarningHistory_Call) Return(_a0 bool, _a1 error) *MockService_DeleteWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_DeleteWarningHistory_Call) RunAndReturn(run func(uuid.UUID) (bool, error)) *MockService_DeleteWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// FindWarnings provides a mock function with given fields: criteria, identity
func (_m *MockService) FindWarnings(criteria modwarn.WarningSearchCriteria, identity *uuid.UUID) (*modwarn.WarningSearchResult, error) {
	ret := _m.Called(criteria, identity)

	if len(ret) == 0 {
		panic("no return value specified for FindWarnings")
	}

	var r0 *modwarn.WarningSearchResult
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, *uuid.UUID) (*modwarn.WarningSearchResult, error)); ok {
		return rf(criteria, identity)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, *uuid.UUID) *modwarn.WarningSearchResult); ok {
		r0 = rf(criteria, identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningSearchResult)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningSearchCriteria, *uuid.UUID) error); ok {
		r1 = rf(criteria, identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_FindWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindWarnings'
type MockService_FindWarnings_Call struct {
	*mock.Call
}

// FindWarnings is a helper method to define mock.On call
//   - criteria modwarn.WarningSearchCriteria
//   - identity *uuid.UUID
func (_e *MockService_Expecter) FindWarnings(criteria interface{}, identity interface{}) *MockService_FindWarnings_Call {
	return &MockService_FindWarnings_Call{Call: _e.mock.On("FindWarnings", criteria, identity)}
}

func (_c *MockService_FindWarnings_Call) Run(run func(criteria modwarn.WarningSearchCriteria, identity *uuid.UUID)) *MockService_FindWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningSearchCriteria), args[1].(*uuid.UUID))
	})
	return _c
}

func (_c *MockService_FindWarnings_Call) Return(_a0 *modwarn.WarningSearchResult, _a1 error) *MockService_FindWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_FindWarnings_Call) RunAndReturn(run func(modwarn.WarningSearchCriteria, *uuid.UUID) (*modwarn.WarningSearchResult, error)) *MockService_FindWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAffectedItems provides a mock function with given fields:
func (_m *MockService) GetAllAffectedItems() ([]modnotif.AffectedItem, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllAffectedItems")
	}

	var r0 []modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.AffectedItem, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.AffectedItem); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllAffectedItems_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAffectedItems'
type MockService_GetAllAffectedItems_Call struct {
	*mock.Call
}

// GetAllAffectedItems is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllAffectedItems() *MockService_GetAllAffectedItems_Call {
	return &MockService_GetAllAffectedItems_Call{Call: _e.mock.On("GetAllAffectedItems")}
}

func (_c *MockService_GetAllAffectedItems_Call) Run(run func()) *MockService_GetAllAffectedItems_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllAffectedItems_Call) Return(_a0 []modnotif.AffectedItem, _a1 error) *MockService_GetAllAffectedItems_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllAffectedItems_Call) RunAndReturn(run func() ([]modnotif.AffectedItem, error)) *MockService_GetAllAffectedItems_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAssets provides a mock function with given fields:
func (_m *MockService) GetAllAssets() ([]modinv.Asset, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllAssets")
	}

	var r0 []modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modinv.Asset, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modinv.Asset); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllAssets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAssets'
type MockService_GetAllAssets_Call struct {
	*mock.Call
}

// GetAllAssets is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllAssets() *MockService_GetAllAssets_Call {
	return &MockService_GetAllAssets_Call{Call: _e.mock.On("GetAllAssets")}
}

func (_c *MockService_GetAllAssets_Call) Run(run func()) *MockService_GetAllAssets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllAssets_Call) Return(_a0 []modinv.Asset, _a1 error) *MockService_GetAllAssets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllAssets_Call) RunAndReturn(run func() ([]modinv.Asset, error)) *MockService_GetAllAssets_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllLocations provides a mock function with given fields:
func (_m *MockService) GetAllLocations() ([]modinv.Location, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllLocations")
	}

	var r0 []modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modinv.Location, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modinv.Location); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllLocations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllLocations'
type MockService_GetAllLocations_Call struct {
	*mock.Call
}

// GetAllLocations is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllLocations() *MockService_GetAllLocations_Call {
	return &MockService_GetAllLocations_Call{Call: _e.mock.On("GetAllLocations")}
}

func (_c *MockService_GetAllLocations_Call) Run(run func()) *MockService_GetAllLocations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllLocations_Call) Return(_a0 []modinv.Location, _a1 error) *MockService_GetAllLocations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllLocations_Call) RunAndReturn(run func() ([]modinv.Location, error)) *MockService_GetAllLocations_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationRules provides a mock function with given fields:
func (_m *MockService) GetAllNotificationRules() ([]modnotif.NotificationRule, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationRules")
	}

	var r0 []modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.NotificationRule, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.NotificationRule); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllNotificationRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationRules'
type MockService_GetAllNotificationRules_Call struct {
	*mock.Call
}

// GetAllNotificationRules is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllNotificationRules() *MockService_GetAllNotificationRules_Call {
	return &MockService_GetAllNotificationRules_Call{Call: _e.mock.On("GetAllNotificationRules")}
}

func (_c *MockService_GetAllNotificationRules_Call) Run(run func()) *MockService_GetAllNotificationRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllNotificationRules_Call) Return(_a0 []modnotif.NotificationRule, _a1 error) *MockService_GetAllNotificationRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllNotificationRules_Call) RunAndReturn(run func() ([]modnotif.NotificationRule, error)) *MockService_GetAllNotificationRules_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationSettings provides a mock function with given fields:
func (_m *MockService) GetAllNotificationSettings() ([]modnotif.NotificationSetting, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationSettings")
	}

	var r0 []modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modnotif.NotificationSetting, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modnotif.NotificationSetting); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllNotificationSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationSettings'
type MockService_GetAllNotificationSettings_Call struct {
	*mock.Call
}

// GetAllNotificationSettings is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllNotificationSettings() *MockService_GetAllNotificationSettings_Call {
	return &MockService_GetAllNotificationSettings_Call{Call: _e.mock.On("GetAllNotificationSettings")}
}

func (_c *MockService_GetAllNotificationSettings_Call) Run(run func()) *MockService_GetAllNotificationSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllNotificationSettings_Call) Return(_a0 []modnotif.NotificationSetting, _a1 error) *MockService_GetAllNotificationSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllNotificationSettings_Call) RunAndReturn(run func() ([]modnotif.NotificationSetting, error)) *MockService_GetAllNotificationSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllUsers provides a mock function with given fields:
func (_m *MockService) GetAllUsers() ([]modusr.User, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllUsers")
	}

	var r0 []modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modusr.User, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modusr.User); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllUsers'
type MockService_GetAllUsers_Call struct {
	*mock.Call
}

// GetAllUsers is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllUsers() *MockService_GetAllUsers_Call {
	return &MockService_GetAllUsers_Call{Call: _e.mock.On("GetAllUsers")}
}

func (_c *MockService_GetAllUsers_Call) Run(run func()) *MockService_GetAllUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllUsers_Call) Return(_a0 []modusr.User, _a1 error) *MockService_GetAllUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllUsers_Call) RunAndReturn(run func() ([]modusr.User, error)) *MockService_GetAllUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarnings provides a mock function with given fields:
func (_m *MockService) GetAllWarnings() ([]modwarn.Warning, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarnings")
	}

	var r0 []modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modwarn.Warning, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modwarn.Warning); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarnings'
type MockService_GetAllWarnings_Call struct {
	*mock.Call
}

// GetAllWarnings is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllWarnings() *MockService_GetAllWarnings_Call {
	return &MockService_GetAllWarnings_Call{Call: _e.mock.On("GetAllWarnings")}
}

func (_c *MockService_GetAllWarnings_Call) Run(run func()) *MockService_GetAllWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllWarnings_Call) Return(_a0 []modwarn.Warning, _a1 error) *MockService_GetAllWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllWarnings_Call) RunAndReturn(run func() ([]modwarn.Warning, error)) *MockService_GetAllWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarningsHistory provides a mock function with given fields:
func (_m *MockService) GetAllWarningsHistory() ([]modwarn.WarningHistory, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarningsHistory")
	}

	var r0 []modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]modwarn.WarningHistory, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []modwarn.WarningHistory); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllWarningsHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarningsHistory'
type MockService_GetAllWarningsHistory_Call struct {
	*mock.Call
}

// GetAllWarningsHistory is a helper method to define mock.On call
func (_e *MockService_Expecter) GetAllWarningsHistory() *MockService_GetAllWarningsHistory_Call {
	return &MockService_GetAllWarningsHistory_Call{Call: _e.mock.On("GetAllWarningsHistory")}
}

func (_c *MockService_GetAllWarningsHistory_Call) Run(run func()) *MockService_GetAllWarningsHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetAllWarningsHistory_Call) Return(_a0 []modwarn.WarningHistory, _a1 error) *MockService_GetAllWarningsHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllWarningsHistory_Call) RunAndReturn(run func() ([]modwarn.WarningHistory, error)) *MockService_GetAllWarningsHistory_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarningsHistoryForWarningId provides a mock function with given fields: warningId
func (_m *MockService) GetAllWarningsHistoryForWarningId(warningId uuid.UUID) ([]modwarn.WarningHistory, error) {
	ret := _m.Called(warningId)

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarningsHistoryForWarningId")
	}

	var r0 []modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) ([]modwarn.WarningHistory, error)); ok {
		return rf(warningId)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) []modwarn.WarningHistory); ok {
		r0 = rf(warningId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(warningId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetAllWarningsHistoryForWarningId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarningsHistoryForWarningId'
type MockService_GetAllWarningsHistoryForWarningId_Call struct {
	*mock.Call
}

// GetAllWarningsHistoryForWarningId is a helper method to define mock.On call
//   - warningId uuid.UUID
func (_e *MockService_Expecter) GetAllWarningsHistoryForWarningId(warningId interface{}) *MockService_GetAllWarningsHistoryForWarningId_Call {
	return &MockService_GetAllWarningsHistoryForWarningId_Call{Call: _e.mock.On("GetAllWarningsHistoryForWarningId", warningId)}
}

func (_c *MockService_GetAllWarningsHistoryForWarningId_Call) Run(run func(warningId uuid.UUID)) *MockService_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_GetAllWarningsHistoryForWarningId_Call) Return(_a0 []modwarn.WarningHistory, _a1 error) *MockService_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetAllWarningsHistoryForWarningId_Call) RunAndReturn(run func(uuid.UUID) ([]modwarn.WarningHistory, error)) *MockService_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Return(run)
	return _c
}

// GetErrorAuditor provides a mock function with given fields:
func (_m *MockService) GetErrorAuditor() kcproc.KafkaErrorAuditor {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetErrorAuditor")
	}

	var r0 kcproc.KafkaErrorAuditor
	if rf, ok := ret.Get(0).(func() kcproc.KafkaErrorAuditor); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(kcproc.KafkaErrorAuditor)
		}
	}

	return r0
}

// MockService_GetErrorAuditor_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetErrorAuditor'
type MockService_GetErrorAuditor_Call struct {
	*mock.Call
}

// GetErrorAuditor is a helper method to define mock.On call
func (_e *MockService_Expecter) GetErrorAuditor() *MockService_GetErrorAuditor_Call {
	return &MockService_GetErrorAuditor_Call{Call: _e.mock.On("GetErrorAuditor")}
}

func (_c *MockService_GetErrorAuditor_Call) Run(run func()) *MockService_GetErrorAuditor_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetErrorAuditor_Call) Return(_a0 kcproc.KafkaErrorAuditor) *MockService_GetErrorAuditor_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetErrorAuditor_Call) RunAndReturn(run func() kcproc.KafkaErrorAuditor) *MockService_GetErrorAuditor_Call {
	_c.Call.Return(run)
	return _c
}

// GetNotificationSettingByUserId provides a mock function with given fields: userId
func (_m *MockService) GetNotificationSettingByUserId(userId uuid.UUID) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(userId)

	if len(ret) == 0 {
		panic("no return value specified for GetNotificationSettingByUserId")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.NotificationSetting, error)); ok {
		return rf(userId)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.NotificationSetting); ok {
		r0 = rf(userId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(userId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetNotificationSettingByUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNotificationSettingByUserId'
type MockService_GetNotificationSettingByUserId_Call struct {
	*mock.Call
}

// GetNotificationSettingByUserId is a helper method to define mock.On call
//   - userId uuid.UUID
func (_e *MockService_Expecter) GetNotificationSettingByUserId(userId interface{}) *MockService_GetNotificationSettingByUserId_Call {
	return &MockService_GetNotificationSettingByUserId_Call{Call: _e.mock.On("GetNotificationSettingByUserId", userId)}
}

func (_c *MockService_GetNotificationSettingByUserId_Call) Run(run func(userId uuid.UUID)) *MockService_GetNotificationSettingByUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_GetNotificationSettingByUserId_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockService_GetNotificationSettingByUserId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetNotificationSettingByUserId_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.NotificationSetting, error)) *MockService_GetNotificationSettingByUserId_Call {
	_c.Call.Return(run)
	return _c
}

// GetProcessor provides a mock function with given fields: name
func (_m *MockService) GetProcessor(name string) kcproc.KafkaProcessor[interface{}] {
	ret := _m.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for GetProcessor")
	}

	var r0 kcproc.KafkaProcessor[interface{}]
	if rf, ok := ret.Get(0).(func(string) kcproc.KafkaProcessor[interface{}]); ok {
		r0 = rf(name)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(kcproc.KafkaProcessor[interface{}])
		}
	}

	return r0
}

// MockService_GetProcessor_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProcessor'
type MockService_GetProcessor_Call struct {
	*mock.Call
}

// GetProcessor is a helper method to define mock.On call
//   - name string
func (_e *MockService_Expecter) GetProcessor(name interface{}) *MockService_GetProcessor_Call {
	return &MockService_GetProcessor_Call{Call: _e.mock.On("GetProcessor", name)}
}

func (_c *MockService_GetProcessor_Call) Run(run func(name string)) *MockService_GetProcessor_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockService_GetProcessor_Call) Return(_a0 kcproc.KafkaProcessor[interface{}]) *MockService_GetProcessor_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetProcessor_Call) RunAndReturn(run func(string) kcproc.KafkaProcessor[interface{}]) *MockService_GetProcessor_Call {
	_c.Call.Return(run)
	return _c
}

// GetProcessorSpecs provides a mock function with given fields:
func (_m *MockService) GetProcessorSpecs() []kcmodel.KafkaProcessorSpec {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetProcessorSpecs")
	}

	var r0 []kcmodel.KafkaProcessorSpec
	if rf, ok := ret.Get(0).(func() []kcmodel.KafkaProcessorSpec); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]kcmodel.KafkaProcessorSpec)
		}
	}

	return r0
}

// MockService_GetProcessorSpecs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProcessorSpecs'
type MockService_GetProcessorSpecs_Call struct {
	*mock.Call
}

// GetProcessorSpecs is a helper method to define mock.On call
func (_e *MockService_Expecter) GetProcessorSpecs() *MockService_GetProcessorSpecs_Call {
	return &MockService_GetProcessorSpecs_Call{Call: _e.mock.On("GetProcessorSpecs")}
}

func (_c *MockService_GetProcessorSpecs_Call) Run(run func()) *MockService_GetProcessorSpecs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetProcessorSpecs_Call) Return(_a0 []kcmodel.KafkaProcessorSpec) *MockService_GetProcessorSpecs_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetProcessorSpecs_Call) RunAndReturn(run func() []kcmodel.KafkaProcessorSpec) *MockService_GetProcessorSpecs_Call {
	_c.Call.Return(run)
	return _c
}

// GetProcessors provides a mock function with given fields:
func (_m *MockService) GetProcessors() []kcproc.KafkaProcessor[interface{}] {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetProcessors")
	}

	var r0 []kcproc.KafkaProcessor[interface{}]
	if rf, ok := ret.Get(0).(func() []kcproc.KafkaProcessor[interface{}]); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]kcproc.KafkaProcessor[interface{}])
		}
	}

	return r0
}

// MockService_GetProcessors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetProcessors'
type MockService_GetProcessors_Call struct {
	*mock.Call
}

// GetProcessors is a helper method to define mock.On call
func (_e *MockService_Expecter) GetProcessors() *MockService_GetProcessors_Call {
	return &MockService_GetProcessors_Call{Call: _e.mock.On("GetProcessors")}
}

func (_c *MockService_GetProcessors_Call) Run(run func()) *MockService_GetProcessors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_GetProcessors_Call) Return(_a0 []kcproc.KafkaProcessor[interface{}]) *MockService_GetProcessors_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_GetProcessors_Call) RunAndReturn(run func() []kcproc.KafkaProcessor[interface{}]) *MockService_GetProcessors_Call {
	_c.Call.Return(run)
	return _c
}

// GetUser provides a mock function with given fields: email
func (_m *MockService) GetUser(email string) (authmod.IAuthUser, error) {
	ret := _m.Called(email)

	if len(ret) == 0 {
		panic("no return value specified for GetUser")
	}

	var r0 authmod.IAuthUser
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (authmod.IAuthUser, error)); ok {
		return rf(email)
	}
	if rf, ok := ret.Get(0).(func(string) authmod.IAuthUser); ok {
		r0 = rf(email)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(authmod.IAuthUser)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(email)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUser'
type MockService_GetUser_Call struct {
	*mock.Call
}

// GetUser is a helper method to define mock.On call
//   - email string
func (_e *MockService_Expecter) GetUser(email interface{}) *MockService_GetUser_Call {
	return &MockService_GetUser_Call{Call: _e.mock.On("GetUser", email)}
}

func (_c *MockService_GetUser_Call) Run(run func(email string)) *MockService_GetUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *MockService_GetUser_Call) Return(_a0 authmod.IAuthUser, _a1 error) *MockService_GetUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetUser_Call) RunAndReturn(run func(string) (authmod.IAuthUser, error)) *MockService_GetUser_Call {
	_c.Call.Return(run)
	return _c
}

// GetWarningForCode provides a mock function with given fields: code
func (_m *MockService) GetWarningForCode(code cjson.JSONB) (*modwarn.Warning, error) {
	ret := _m.Called(code)

	if len(ret) == 0 {
		panic("no return value specified for GetWarningForCode")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(cjson.JSONB) (*modwarn.Warning, error)); ok {
		return rf(code)
	}
	if rf, ok := ret.Get(0).(func(cjson.JSONB) *modwarn.Warning); ok {
		r0 = rf(code)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(cjson.JSONB) error); ok {
		r1 = rf(code)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_GetWarningForCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWarningForCode'
type MockService_GetWarningForCode_Call struct {
	*mock.Call
}

// GetWarningForCode is a helper method to define mock.On call
//   - code cjson.JSONB
func (_e *MockService_Expecter) GetWarningForCode(code interface{}) *MockService_GetWarningForCode_Call {
	return &MockService_GetWarningForCode_Call{Call: _e.mock.On("GetWarningForCode", code)}
}

func (_c *MockService_GetWarningForCode_Call) Run(run func(code cjson.JSONB)) *MockService_GetWarningForCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(cjson.JSONB))
	})
	return _c
}

func (_c *MockService_GetWarningForCode_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockService_GetWarningForCode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_GetWarningForCode_Call) RunAndReturn(run func(cjson.JSONB) (*modwarn.Warning, error)) *MockService_GetWarningForCode_Call {
	_c.Call.Return(run)
	return _c
}

// Process provides a mock function with given fields: busEvent
func (_m *MockService) Process(busEvent *invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage]) (interface{}, error) {
	ret := _m.Called(busEvent)

	if len(ret) == 0 {
		panic("no return value specified for Process")
	}

	var r0 interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(*invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage]) (interface{}, error)); ok {
		return rf(busEvent)
	}
	if rf, ok := ret.Get(0).(func(*invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage]) interface{}); ok {
		r0 = rf(busEvent)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(*invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage]) error); ok {
		r1 = rf(busEvent)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_Process_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Process'
type MockService_Process_Call struct {
	*mock.Call
}

// Process is a helper method to define mock.On call
//   - busEvent *invmod.BusEvent[modbus.BusMetaDataEventAction,json.RawMessage]
func (_e *MockService_Expecter) Process(busEvent interface{}) *MockService_Process_Call {
	return &MockService_Process_Call{Call: _e.mock.On("Process", busEvent)}
}

func (_c *MockService_Process_Call) Run(run func(busEvent *invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage])) *MockService_Process_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage]))
	})
	return _c
}

func (_c *MockService_Process_Call) Return(_a0 interface{}, _a1 error) *MockService_Process_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_Process_Call) RunAndReturn(run func(*invmod.BusEvent[modbus.BusMetaDataEventAction, json.RawMessage]) (interface{}, error)) *MockService_Process_Call {
	_c.Call.Return(run)
	return _c
}

// ProcessWarning provides a mock function with given fields: _a0
func (_m *MockService) ProcessWarning(_a0 modwarn.WarningDefinition) error {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for ProcessWarning")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningDefinition) error); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_ProcessWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ProcessWarning'
type MockService_ProcessWarning_Call struct {
	*mock.Call
}

// ProcessWarning is a helper method to define mock.On call
//   - _a0 modwarn.WarningDefinition
func (_e *MockService_Expecter) ProcessWarning(_a0 interface{}) *MockService_ProcessWarning_Call {
	return &MockService_ProcessWarning_Call{Call: _e.mock.On("ProcessWarning", _a0)}
}

func (_c *MockService_ProcessWarning_Call) Run(run func(_a0 modwarn.WarningDefinition)) *MockService_ProcessWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningDefinition))
	})
	return _c
}

func (_c *MockService_ProcessWarning_Call) Return(_a0 error) *MockService_ProcessWarning_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_ProcessWarning_Call) RunAndReturn(run func(modwarn.WarningDefinition) error) *MockService_ProcessWarning_Call {
	_c.Call.Return(run)
	return _c
}

// RefreshAllAssets provides a mock function with given fields:
func (_m *MockService) RefreshAllAssets() ([]invmod.InventoryInconsistency, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefreshAllAssets")
	}

	var r0 []invmod.InventoryInconsistency
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]invmod.InventoryInconsistency, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []invmod.InventoryInconsistency); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]invmod.InventoryInconsistency)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RefreshAllAssets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshAllAssets'
type MockService_RefreshAllAssets_Call struct {
	*mock.Call
}

// RefreshAllAssets is a helper method to define mock.On call
func (_e *MockService_Expecter) RefreshAllAssets() *MockService_RefreshAllAssets_Call {
	return &MockService_RefreshAllAssets_Call{Call: _e.mock.On("RefreshAllAssets")}
}

func (_c *MockService_RefreshAllAssets_Call) Run(run func()) *MockService_RefreshAllAssets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_RefreshAllAssets_Call) Return(_a0 []invmod.InventoryInconsistency, _a1 error) *MockService_RefreshAllAssets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RefreshAllAssets_Call) RunAndReturn(run func() ([]invmod.InventoryInconsistency, error)) *MockService_RefreshAllAssets_Call {
	_c.Call.Return(run)
	return _c
}

// RefreshAllLocations provides a mock function with given fields:
func (_m *MockService) RefreshAllLocations() ([]invmod.InventoryInconsistency, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for RefreshAllLocations")
	}

	var r0 []invmod.InventoryInconsistency
	var r1 error
	if rf, ok := ret.Get(0).(func() ([]invmod.InventoryInconsistency, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() []invmod.InventoryInconsistency); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]invmod.InventoryInconsistency)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RefreshAllLocations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshAllLocations'
type MockService_RefreshAllLocations_Call struct {
	*mock.Call
}

// RefreshAllLocations is a helper method to define mock.On call
func (_e *MockService_Expecter) RefreshAllLocations() *MockService_RefreshAllLocations_Call {
	return &MockService_RefreshAllLocations_Call{Call: _e.mock.On("RefreshAllLocations")}
}

func (_c *MockService_RefreshAllLocations_Call) Run(run func()) *MockService_RefreshAllLocations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_RefreshAllLocations_Call) Return(_a0 []invmod.InventoryInconsistency, _a1 error) *MockService_RefreshAllLocations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RefreshAllLocations_Call) RunAndReturn(run func() ([]invmod.InventoryInconsistency, error)) *MockService_RefreshAllLocations_Call {
	_c.Call.Return(run)
	return _c
}

// RefreshAllUsers provides a mock function with given fields: flagErrors
func (_m *MockService) RefreshAllUsers(flagErrors bool) ([]invmod.InventoryInconsistency, error) {
	ret := _m.Called(flagErrors)

	if len(ret) == 0 {
		panic("no return value specified for RefreshAllUsers")
	}

	var r0 []invmod.InventoryInconsistency
	var r1 error
	if rf, ok := ret.Get(0).(func(bool) ([]invmod.InventoryInconsistency, error)); ok {
		return rf(flagErrors)
	}
	if rf, ok := ret.Get(0).(func(bool) []invmod.InventoryInconsistency); ok {
		r0 = rf(flagErrors)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]invmod.InventoryInconsistency)
		}
	}

	if rf, ok := ret.Get(1).(func(bool) error); ok {
		r1 = rf(flagErrors)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RefreshAllUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RefreshAllUsers'
type MockService_RefreshAllUsers_Call struct {
	*mock.Call
}

// RefreshAllUsers is a helper method to define mock.On call
//   - flagErrors bool
func (_e *MockService_Expecter) RefreshAllUsers(flagErrors interface{}) *MockService_RefreshAllUsers_Call {
	return &MockService_RefreshAllUsers_Call{Call: _e.mock.On("RefreshAllUsers", flagErrors)}
}

func (_c *MockService_RefreshAllUsers_Call) Run(run func(flagErrors bool)) *MockService_RefreshAllUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(bool))
	})
	return _c
}

func (_c *MockService_RefreshAllUsers_Call) Return(_a0 []invmod.InventoryInconsistency, _a1 error) *MockService_RefreshAllUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RefreshAllUsers_Call) RunAndReturn(run func(bool) ([]invmod.InventoryInconsistency, error)) *MockService_RefreshAllUsers_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveAffectedItem provides a mock function with given fields: identity
func (_m *MockService) RetrieveAffectedItem(identity uuid.UUID) (*modnotif.AffectedItem, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.AffectedItem, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.AffectedItem); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveAffectedItem'
type MockService_RetrieveAffectedItem_Call struct {
	*mock.Call
}

// RetrieveAffectedItem is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveAffectedItem(identity interface{}) *MockService_RetrieveAffectedItem_Call {
	return &MockService_RetrieveAffectedItem_Call{Call: _e.mock.On("RetrieveAffectedItem", identity)}
}

func (_c *MockService_RetrieveAffectedItem_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockService_RetrieveAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveAffectedItem_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.AffectedItem, error)) *MockService_RetrieveAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveAsset provides a mock function with given fields: identity
func (_m *MockService) RetrieveAsset(identity uuid.UUID) (*modinv.Asset, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modinv.Asset, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modinv.Asset); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveAsset'
type MockService_RetrieveAsset_Call struct {
	*mock.Call
}

// RetrieveAsset is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveAsset(identity interface{}) *MockService_RetrieveAsset_Call {
	return &MockService_RetrieveAsset_Call{Call: _e.mock.On("RetrieveAsset", identity)}
}

func (_c *MockService_RetrieveAsset_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockService_RetrieveAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveAsset_Call) RunAndReturn(run func(uuid.UUID) (*modinv.Asset, error)) *MockService_RetrieveAsset_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveLocation provides a mock function with given fields: identity
func (_m *MockService) RetrieveLocation(identity uuid.UUID) (*modinv.Location, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modinv.Location, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modinv.Location); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveLocation'
type MockService_RetrieveLocation_Call struct {
	*mock.Call
}

// RetrieveLocation is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveLocation(identity interface{}) *MockService_RetrieveLocation_Call {
	return &MockService_RetrieveLocation_Call{Call: _e.mock.On("RetrieveLocation", identity)}
}

func (_c *MockService_RetrieveLocation_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockService_RetrieveLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveLocation_Call) RunAndReturn(run func(uuid.UUID) (*modinv.Location, error)) *MockService_RetrieveLocation_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationRule provides a mock function with given fields: identity
func (_m *MockService) RetrieveNotificationRule(identity uuid.UUID) (*modnotif.NotificationRule, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.NotificationRule, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.NotificationRule); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationRule'
type MockService_RetrieveNotificationRule_Call struct {
	*mock.Call
}

// RetrieveNotificationRule is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveNotificationRule(identity interface{}) *MockService_RetrieveNotificationRule_Call {
	return &MockService_RetrieveNotificationRule_Call{Call: _e.mock.On("RetrieveNotificationRule", identity)}
}

func (_c *MockService_RetrieveNotificationRule_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockService_RetrieveNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveNotificationRule_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.NotificationRule, error)) *MockService_RetrieveNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationSetting provides a mock function with given fields: identity
func (_m *MockService) RetrieveNotificationSetting(identity uuid.UUID) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modnotif.NotificationSetting, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modnotif.NotificationSetting); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationSetting'
type MockService_RetrieveNotificationSetting_Call struct {
	*mock.Call
}

// RetrieveNotificationSetting is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveNotificationSetting(identity interface{}) *MockService_RetrieveNotificationSetting_Call {
	return &MockService_RetrieveNotificationSetting_Call{Call: _e.mock.On("RetrieveNotificationSetting", identity)}
}

func (_c *MockService_RetrieveNotificationSetting_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockService_RetrieveNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveNotificationSetting_Call) RunAndReturn(run func(uuid.UUID) (*modnotif.NotificationSetting, error)) *MockService_RetrieveNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveUser provides a mock function with given fields: identity
func (_m *MockService) RetrieveUser(identity uuid.UUID) (*modusr.User, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modusr.User, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modusr.User); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveUser'
type MockService_RetrieveUser_Call struct {
	*mock.Call
}

// RetrieveUser is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveUser(identity interface{}) *MockService_RetrieveUser_Call {
	return &MockService_RetrieveUser_Call{Call: _e.mock.On("RetrieveUser", identity)}
}

func (_c *MockService_RetrieveUser_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveUser_Call) Return(_a0 *modusr.User, _a1 error) *MockService_RetrieveUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveUser_Call) RunAndReturn(run func(uuid.UUID) (*modusr.User, error)) *MockService_RetrieveUser_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveWarning provides a mock function with given fields: identity
func (_m *MockService) RetrieveWarning(identity uuid.UUID) (*modwarn.Warning, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modwarn.Warning, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modwarn.Warning); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveWarning'
type MockService_RetrieveWarning_Call struct {
	*mock.Call
}

// RetrieveWarning is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveWarning(identity interface{}) *MockService_RetrieveWarning_Call {
	return &MockService_RetrieveWarning_Call{Call: _e.mock.On("RetrieveWarning", identity)}
}

func (_c *MockService_RetrieveWarning_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockService_RetrieveWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveWarning_Call) RunAndReturn(run func(uuid.UUID) (*modwarn.Warning, error)) *MockService_RetrieveWarning_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveWarningHistory provides a mock function with given fields: identity
func (_m *MockService) RetrieveWarningHistory(identity uuid.UUID) (*modwarn.WarningHistory, error) {
	ret := _m.Called(identity)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID) (*modwarn.WarningHistory, error)); ok {
		return rf(identity)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID) *modwarn.WarningHistory); ok {
		r0 = rf(identity)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID) error); ok {
		r1 = rf(identity)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_RetrieveWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveWarningHistory'
type MockService_RetrieveWarningHistory_Call struct {
	*mock.Call
}

// RetrieveWarningHistory is a helper method to define mock.On call
//   - identity uuid.UUID
func (_e *MockService_Expecter) RetrieveWarningHistory(identity interface{}) *MockService_RetrieveWarningHistory_Call {
	return &MockService_RetrieveWarningHistory_Call{Call: _e.mock.On("RetrieveWarningHistory", identity)}
}

func (_c *MockService_RetrieveWarningHistory_Call) Run(run func(identity uuid.UUID)) *MockService_RetrieveWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID))
	})
	return _c
}

func (_c *MockService_RetrieveWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockService_RetrieveWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_RetrieveWarningHistory_Call) RunAndReturn(run func(uuid.UUID) (*modwarn.WarningHistory, error)) *MockService_RetrieveWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// SomethingHappened provides a mock function with given fields:
func (_m *MockService) SomethingHappened() {
	_m.Called()
}

// MockService_SomethingHappened_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SomethingHappened'
type MockService_SomethingHappened_Call struct {
	*mock.Call
}

// SomethingHappened is a helper method to define mock.On call
func (_e *MockService_Expecter) SomethingHappened() *MockService_SomethingHappened_Call {
	return &MockService_SomethingHappened_Call{Call: _e.mock.On("SomethingHappened")}
}

func (_c *MockService_SomethingHappened_Call) Run(run func()) *MockService_SomethingHappened_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_SomethingHappened_Call) Return() *MockService_SomethingHappened_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockService_SomethingHappened_Call) RunAndReturn(run func()) *MockService_SomethingHappened_Call {
	_c.Call.Return(run)
	return _c
}

// StartProcessor provides a mock function with given fields: specs
func (_m *MockService) StartProcessor(specs ...kcmodel.KafkaProcessorSpec) error {
	_va := make([]interface{}, len(specs))
	for _i := range specs {
		_va[_i] = specs[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for StartProcessor")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(...kcmodel.KafkaProcessorSpec) error); ok {
		r0 = rf(specs...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_StartProcessor_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartProcessor'
type MockService_StartProcessor_Call struct {
	*mock.Call
}

// StartProcessor is a helper method to define mock.On call
//   - specs ...kcmodel.KafkaProcessorSpec
func (_e *MockService_Expecter) StartProcessor(specs ...interface{}) *MockService_StartProcessor_Call {
	return &MockService_StartProcessor_Call{Call: _e.mock.On("StartProcessor",
		append([]interface{}{}, specs...)...)}
}

func (_c *MockService_StartProcessor_Call) Run(run func(specs ...kcmodel.KafkaProcessorSpec)) *MockService_StartProcessor_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]kcmodel.KafkaProcessorSpec, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(kcmodel.KafkaProcessorSpec)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockService_StartProcessor_Call) Return(_a0 error) *MockService_StartProcessor_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_StartProcessor_Call) RunAndReturn(run func(...kcmodel.KafkaProcessorSpec) error) *MockService_StartProcessor_Call {
	_c.Call.Return(run)
	return _c
}

// StopAllProcessors provides a mock function with given fields:
func (_m *MockService) StopAllProcessors() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for StopAllProcessors")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_StopAllProcessors_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopAllProcessors'
type MockService_StopAllProcessors_Call struct {
	*mock.Call
}

// StopAllProcessors is a helper method to define mock.On call
func (_e *MockService_Expecter) StopAllProcessors() *MockService_StopAllProcessors_Call {
	return &MockService_StopAllProcessors_Call{Call: _e.mock.On("StopAllProcessors")}
}

func (_c *MockService_StopAllProcessors_Call) Run(run func()) *MockService_StopAllProcessors_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockService_StopAllProcessors_Call) Return(_a0 error) *MockService_StopAllProcessors_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_StopAllProcessors_Call) RunAndReturn(run func() error) *MockService_StopAllProcessors_Call {
	_c.Call.Return(run)
	return _c
}

// StopProcessor provides a mock function with given fields: names
func (_m *MockService) StopProcessor(names ...string) error {
	_va := make([]interface{}, len(names))
	for _i := range names {
		_va[_i] = names[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for StopProcessor")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(...string) error); ok {
		r0 = rf(names...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockService_StopProcessor_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopProcessor'
type MockService_StopProcessor_Call struct {
	*mock.Call
}

// StopProcessor is a helper method to define mock.On call
//   - names ...string
func (_e *MockService_Expecter) StopProcessor(names ...interface{}) *MockService_StopProcessor_Call {
	return &MockService_StopProcessor_Call{Call: _e.mock.On("StopProcessor",
		append([]interface{}{}, names...)...)}
}

func (_c *MockService_StopProcessor_Call) Run(run func(names ...string)) *MockService_StopProcessor_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]string, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(string)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockService_StopProcessor_Call) Return(_a0 error) *MockService_StopProcessor_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_StopProcessor_Call) RunAndReturn(run func(...string) error) *MockService_StopProcessor_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAffectedItem provides a mock function with given fields: item
func (_m *MockService) UpdateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	ret := _m.Called(item)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)); ok {
		return rf(item)
	}
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem) *modnotif.AffectedItem); ok {
		r0 = rf(item)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.AffectedItem) error); ok {
		r1 = rf(item)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAffectedItem'
type MockService_UpdateAffectedItem_Call struct {
	*mock.Call
}

// UpdateAffectedItem is a helper method to define mock.On call
//   - item modnotif.AffectedItem
func (_e *MockService_Expecter) UpdateAffectedItem(item interface{}) *MockService_UpdateAffectedItem_Call {
	return &MockService_UpdateAffectedItem_Call{Call: _e.mock.On("UpdateAffectedItem", item)}
}

func (_c *MockService_UpdateAffectedItem_Call) Run(run func(item modnotif.AffectedItem)) *MockService_UpdateAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.AffectedItem))
	})
	return _c
}

func (_c *MockService_UpdateAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockService_UpdateAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateAffectedItem_Call) RunAndReturn(run func(modnotif.AffectedItem) (*modnotif.AffectedItem, error)) *MockService_UpdateAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAsset provides a mock function with given fields: asset
func (_m *MockService) UpdateAsset(asset modinv.Asset) (*modinv.Asset, error) {
	ret := _m.Called(asset)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Asset) (*modinv.Asset, error)); ok {
		return rf(asset)
	}
	if rf, ok := ret.Get(0).(func(modinv.Asset) *modinv.Asset); ok {
		r0 = rf(asset)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Asset) error); ok {
		r1 = rf(asset)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAsset'
type MockService_UpdateAsset_Call struct {
	*mock.Call
}

// UpdateAsset is a helper method to define mock.On call
//   - asset modinv.Asset
func (_e *MockService_Expecter) UpdateAsset(asset interface{}) *MockService_UpdateAsset_Call {
	return &MockService_UpdateAsset_Call{Call: _e.mock.On("UpdateAsset", asset)}
}

func (_c *MockService_UpdateAsset_Call) Run(run func(asset modinv.Asset)) *MockService_UpdateAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Asset))
	})
	return _c
}

func (_c *MockService_UpdateAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockService_UpdateAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateAsset_Call) RunAndReturn(run func(modinv.Asset) (*modinv.Asset, error)) *MockService_UpdateAsset_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLocation provides a mock function with given fields: location
func (_m *MockService) UpdateLocation(location modinv.Location) (*modinv.Location, error) {
	ret := _m.Called(location)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Location) (*modinv.Location, error)); ok {
		return rf(location)
	}
	if rf, ok := ret.Get(0).(func(modinv.Location) *modinv.Location); ok {
		r0 = rf(location)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Location) error); ok {
		r1 = rf(location)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLocation'
type MockService_UpdateLocation_Call struct {
	*mock.Call
}

// UpdateLocation is a helper method to define mock.On call
//   - location modinv.Location
func (_e *MockService_Expecter) UpdateLocation(location interface{}) *MockService_UpdateLocation_Call {
	return &MockService_UpdateLocation_Call{Call: _e.mock.On("UpdateLocation", location)}
}

func (_c *MockService_UpdateLocation_Call) Run(run func(location modinv.Location)) *MockService_UpdateLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modinv.Location))
	})
	return _c
}

func (_c *MockService_UpdateLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockService_UpdateLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateLocation_Call) RunAndReturn(run func(modinv.Location) (*modinv.Location, error)) *MockService_UpdateLocation_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationRule provides a mock function with given fields: rule
func (_m *MockService) UpdateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	ret := _m.Called(rule)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)); ok {
		return rf(rule)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule) *modnotif.NotificationRule); ok {
		r0 = rf(rule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationRule) error); ok {
		r1 = rf(rule)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationRule'
type MockService_UpdateNotificationRule_Call struct {
	*mock.Call
}

// UpdateNotificationRule is a helper method to define mock.On call
//   - rule modnotif.NotificationRule
func (_e *MockService_Expecter) UpdateNotificationRule(rule interface{}) *MockService_UpdateNotificationRule_Call {
	return &MockService_UpdateNotificationRule_Call{Call: _e.mock.On("UpdateNotificationRule", rule)}
}

func (_c *MockService_UpdateNotificationRule_Call) Run(run func(rule modnotif.NotificationRule)) *MockService_UpdateNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationRule))
	})
	return _c
}

func (_c *MockService_UpdateNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockService_UpdateNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateNotificationRule_Call) RunAndReturn(run func(modnotif.NotificationRule) (*modnotif.NotificationRule, error)) *MockService_UpdateNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationSetting provides a mock function with given fields: setting
func (_m *MockService) UpdateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	ret := _m.Called(setting)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)); ok {
		return rf(setting)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting) *modnotif.NotificationSetting); ok {
		r0 = rf(setting)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationSetting) error); ok {
		r1 = rf(setting)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationSetting'
type MockService_UpdateNotificationSetting_Call struct {
	*mock.Call
}

// UpdateNotificationSetting is a helper method to define mock.On call
//   - setting modnotif.NotificationSetting
func (_e *MockService_Expecter) UpdateNotificationSetting(setting interface{}) *MockService_UpdateNotificationSetting_Call {
	return &MockService_UpdateNotificationSetting_Call{Call: _e.mock.On("UpdateNotificationSetting", setting)}
}

func (_c *MockService_UpdateNotificationSetting_Call) Run(run func(setting modnotif.NotificationSetting)) *MockService_UpdateNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modnotif.NotificationSetting))
	})
	return _c
}

func (_c *MockService_UpdateNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockService_UpdateNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateNotificationSetting_Call) RunAndReturn(run func(modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)) *MockService_UpdateNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUser provides a mock function with given fields: user
func (_m *MockService) UpdateUser(user modusr.User) (*modusr.User, error) {
	ret := _m.Called(user)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(modusr.User) (*modusr.User, error)); ok {
		return rf(user)
	}
	if rf, ok := ret.Get(0).(func(modusr.User) *modusr.User); ok {
		r0 = rf(user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(modusr.User) error); ok {
		r1 = rf(user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUser'
type MockService_UpdateUser_Call struct {
	*mock.Call
}

// UpdateUser is a helper method to define mock.On call
//   - user modusr.User
func (_e *MockService_Expecter) UpdateUser(user interface{}) *MockService_UpdateUser_Call {
	return &MockService_UpdateUser_Call{Call: _e.mock.On("UpdateUser", user)}
}

func (_c *MockService_UpdateUser_Call) Run(run func(user modusr.User)) *MockService_UpdateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modusr.User))
	})
	return _c
}

func (_c *MockService_UpdateUser_Call) Return(_a0 *modusr.User, _a1 error) *MockService_UpdateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateUser_Call) RunAndReturn(run func(modusr.User) (*modusr.User, error)) *MockService_UpdateUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarning provides a mock function with given fields: _a0
func (_m *MockService) UpdateWarning(_a0 modwarn.Warning) (*modwarn.Warning, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.Warning) (*modwarn.Warning, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(modwarn.Warning) *modwarn.Warning); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.Warning) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarning'
type MockService_UpdateWarning_Call struct {
	*mock.Call
}

// UpdateWarning is a helper method to define mock.On call
//   - _a0 modwarn.Warning
func (_e *MockService_Expecter) UpdateWarning(_a0 interface{}) *MockService_UpdateWarning_Call {
	return &MockService_UpdateWarning_Call{Call: _e.mock.On("UpdateWarning", _a0)}
}

func (_c *MockService_UpdateWarning_Call) Run(run func(_a0 modwarn.Warning)) *MockService_UpdateWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.Warning))
	})
	return _c
}

func (_c *MockService_UpdateWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockService_UpdateWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateWarning_Call) RunAndReturn(run func(modwarn.Warning) (*modwarn.Warning, error)) *MockService_UpdateWarning_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningHistory provides a mock function with given fields: warningHistory
func (_m *MockService) UpdateWarningHistory(warningHistory modwarn.WarningHistory) (*modwarn.WarningHistory, error) {
	ret := _m.Called(warningHistory)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory) error); ok {
		r1 = rf(warningHistory)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningHistory'
type MockService_UpdateWarningHistory_Call struct {
	*mock.Call
}

// UpdateWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
func (_e *MockService_Expecter) UpdateWarningHistory(warningHistory interface{}) *MockService_UpdateWarningHistory_Call {
	return &MockService_UpdateWarningHistory_Call{Call: _e.mock.On("UpdateWarningHistory", warningHistory)}
}

func (_c *MockService_UpdateWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory)) *MockService_UpdateWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningHistory))
	})
	return _c
}

func (_c *MockService_UpdateWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockService_UpdateWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)) *MockService_UpdateWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningLastSeenTimestamp provides a mock function with given fields: identity, timestamp
func (_m *MockService) UpdateWarningLastSeenTimestamp(identity uuid.UUID, timestamp time.Time) (*modwarn.Warning, error) {
	ret := _m.Called(identity, timestamp)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningLastSeenTimestamp")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, time.Time) (*modwarn.Warning, error)); ok {
		return rf(identity, timestamp)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, time.Time) *modwarn.Warning); ok {
		r0 = rf(identity, timestamp)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, time.Time) error); ok {
		r1 = rf(identity, timestamp)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateWarningLastSeenTimestamp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningLastSeenTimestamp'
type MockService_UpdateWarningLastSeenTimestamp_Call struct {
	*mock.Call
}

// UpdateWarningLastSeenTimestamp is a helper method to define mock.On call
//   - identity uuid.UUID
//   - timestamp time.Time
func (_e *MockService_Expecter) UpdateWarningLastSeenTimestamp(identity interface{}, timestamp interface{}) *MockService_UpdateWarningLastSeenTimestamp_Call {
	return &MockService_UpdateWarningLastSeenTimestamp_Call{Call: _e.mock.On("UpdateWarningLastSeenTimestamp", identity, timestamp)}
}

func (_c *MockService_UpdateWarningLastSeenTimestamp_Call) Run(run func(identity uuid.UUID, timestamp time.Time)) *MockService_UpdateWarningLastSeenTimestamp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID), args[1].(time.Time))
	})
	return _c
}

func (_c *MockService_UpdateWarningLastSeenTimestamp_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockService_UpdateWarningLastSeenTimestamp_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateWarningLastSeenTimestamp_Call) RunAndReturn(run func(uuid.UUID, time.Time) (*modwarn.Warning, error)) *MockService_UpdateWarningLastSeenTimestamp_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningState provides a mock function with given fields: identity, newState
func (_m *MockService) UpdateWarningState(identity uuid.UUID, newState modwarn.WarningState) (*modwarn.Warning, error) {
	ret := _m.Called(identity, newState)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningState")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, modwarn.WarningState) (*modwarn.Warning, error)); ok {
		return rf(identity, newState)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, modwarn.WarningState) *modwarn.Warning); ok {
		r0 = rf(identity, newState)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, modwarn.WarningState) error); ok {
		r1 = rf(identity, newState)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpdateWarningState_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningState'
type MockService_UpdateWarningState_Call struct {
	*mock.Call
}

// UpdateWarningState is a helper method to define mock.On call
//   - identity uuid.UUID
//   - newState modwarn.WarningState
func (_e *MockService_Expecter) UpdateWarningState(identity interface{}, newState interface{}) *MockService_UpdateWarningState_Call {
	return &MockService_UpdateWarningState_Call{Call: _e.mock.On("UpdateWarningState", identity, newState)}
}

func (_c *MockService_UpdateWarningState_Call) Run(run func(identity uuid.UUID, newState modwarn.WarningState)) *MockService_UpdateWarningState_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(uuid.UUID), args[1].(modwarn.WarningState))
	})
	return _c
}

func (_c *MockService_UpdateWarningState_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockService_UpdateWarningState_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpdateWarningState_Call) RunAndReturn(run func(uuid.UUID, modwarn.WarningState) (*modwarn.Warning, error)) *MockService_UpdateWarningState_Call {
	_c.Call.Return(run)
	return _c
}

// UpsertWarningHistory provides a mock function with given fields: warningHistory
func (_m *MockService) UpsertWarningHistory(warningHistory modwarn.WarningHistory) (*modwarn.WarningHistory, error) {
	ret := _m.Called(warningHistory)

	if len(ret) == 0 {
		panic("no return value specified for UpsertWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory) error); ok {
		r1 = rf(warningHistory)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UpsertWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpsertWarningHistory'
type MockService_UpsertWarningHistory_Call struct {
	*mock.Call
}

// UpsertWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
func (_e *MockService_Expecter) UpsertWarningHistory(warningHistory interface{}) *MockService_UpsertWarningHistory_Call {
	return &MockService_UpsertWarningHistory_Call{Call: _e.mock.On("UpsertWarningHistory", warningHistory)}
}

func (_c *MockService_UpsertWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory)) *MockService_UpsertWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(modwarn.WarningHistory))
	})
	return _c
}

func (_c *MockService_UpsertWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockService_UpsertWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UpsertWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory) (*modwarn.WarningHistory, error)) *MockService_UpsertWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// UserHasRole provides a mock function with given fields: authUser, authUserRole
func (_m *MockService) UserHasRole(authUser authmod.IAuthUser, authUserRole []authmod.IAuthUserRole) (bool, error) {
	ret := _m.Called(authUser, authUserRole)

	if len(ret) == 0 {
		panic("no return value specified for UserHasRole")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(authmod.IAuthUser, []authmod.IAuthUserRole) (bool, error)); ok {
		return rf(authUser, authUserRole)
	}
	if rf, ok := ret.Get(0).(func(authmod.IAuthUser, []authmod.IAuthUserRole) bool); ok {
		r0 = rf(authUser, authUserRole)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(authmod.IAuthUser, []authmod.IAuthUserRole) error); ok {
		r1 = rf(authUser, authUserRole)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockService_UserHasRole_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UserHasRole'
type MockService_UserHasRole_Call struct {
	*mock.Call
}

// UserHasRole is a helper method to define mock.On call
//   - authUser authmod.IAuthUser
//   - authUserRole []authmod.IAuthUserRole
func (_e *MockService_Expecter) UserHasRole(authUser interface{}, authUserRole interface{}) *MockService_UserHasRole_Call {
	return &MockService_UserHasRole_Call{Call: _e.mock.On("UserHasRole", authUser, authUserRole)}
}

func (_c *MockService_UserHasRole_Call) Run(run func(authUser authmod.IAuthUser, authUserRole []authmod.IAuthUserRole)) *MockService_UserHasRole_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(authmod.IAuthUser), args[1].([]authmod.IAuthUserRole))
	})
	return _c
}

func (_c *MockService_UserHasRole_Call) Return(_a0 bool, _a1 error) *MockService_UserHasRole_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockService_UserHasRole_Call) RunAndReturn(run func(authmod.IAuthUser, []authmod.IAuthUserRole) (bool, error)) *MockService_UserHasRole_Call {
	_c.Call.Return(run)
	return _c
}

// WithCalculationService provides a mock function with given fields: calculationService
func (_m *MockService) WithCalculationService(calculationService calc.CalculationsService) Service {
	ret := _m.Called(calculationService)

	if len(ret) == 0 {
		panic("no return value specified for WithCalculationService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(calc.CalculationsService) Service); ok {
		r0 = rf(calculationService)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithCalculationService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithCalculationService'
type MockService_WithCalculationService_Call struct {
	*mock.Call
}

// WithCalculationService is a helper method to define mock.On call
//   - calculationService calc.CalculationsService
func (_e *MockService_Expecter) WithCalculationService(calculationService interface{}) *MockService_WithCalculationService_Call {
	return &MockService_WithCalculationService_Call{Call: _e.mock.On("WithCalculationService", calculationService)}
}

func (_c *MockService_WithCalculationService_Call) Run(run func(calculationService calc.CalculationsService)) *MockService_WithCalculationService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(calc.CalculationsService))
	})
	return _c
}

func (_c *MockService_WithCalculationService_Call) Return(_a0 Service) *MockService_WithCalculationService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithCalculationService_Call) RunAndReturn(run func(calc.CalculationsService) Service) *MockService_WithCalculationService_Call {
	_c.Call.Return(run)
	return _c
}

// WithHistoryWarningService provides a mock function with given fields: historyWarningService
func (_m *MockService) WithHistoryWarningService(historyWarningService warning.WarningService) Service {
	ret := _m.Called(historyWarningService)

	if len(ret) == 0 {
		panic("no return value specified for WithHistoryWarningService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(warning.WarningService) Service); ok {
		r0 = rf(historyWarningService)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithHistoryWarningService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithHistoryWarningService'
type MockService_WithHistoryWarningService_Call struct {
	*mock.Call
}

// WithHistoryWarningService is a helper method to define mock.On call
//   - historyWarningService warning.WarningService
func (_e *MockService_Expecter) WithHistoryWarningService(historyWarningService interface{}) *MockService_WithHistoryWarningService_Call {
	return &MockService_WithHistoryWarningService_Call{Call: _e.mock.On("WithHistoryWarningService", historyWarningService)}
}

func (_c *MockService_WithHistoryWarningService_Call) Run(run func(historyWarningService warning.WarningService)) *MockService_WithHistoryWarningService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(warning.WarningService))
	})
	return _c
}

func (_c *MockService_WithHistoryWarningService_Call) Return(_a0 Service) *MockService_WithHistoryWarningService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithHistoryWarningService_Call) RunAndReturn(run func(warning.WarningService) Service) *MockService_WithHistoryWarningService_Call {
	_c.Call.Return(run)
	return _c
}

// WithInventoryService provides a mock function with given fields: inventory
func (_m *MockService) WithInventoryService(inventory inv.InvService) Service {
	ret := _m.Called(inventory)

	if len(ret) == 0 {
		panic("no return value specified for WithInventoryService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(inv.InvService) Service); ok {
		r0 = rf(inventory)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithInventoryService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithInventoryService'
type MockService_WithInventoryService_Call struct {
	*mock.Call
}

// WithInventoryService is a helper method to define mock.On call
//   - inventory inv.InvService
func (_e *MockService_Expecter) WithInventoryService(inventory interface{}) *MockService_WithInventoryService_Call {
	return &MockService_WithInventoryService_Call{Call: _e.mock.On("WithInventoryService", inventory)}
}

func (_c *MockService_WithInventoryService_Call) Run(run func(inventory inv.InvService)) *MockService_WithInventoryService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(inv.InvService))
	})
	return _c
}

func (_c *MockService_WithInventoryService_Call) Return(_a0 Service) *MockService_WithInventoryService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithInventoryService_Call) RunAndReturn(run func(inv.InvService) Service) *MockService_WithInventoryService_Call {
	_c.Call.Return(run)
	return _c
}

// WithKafkaManager provides a mock function with given fields: kafkaManager
func (_m *MockService) WithKafkaManager(kafkaManager kcproc.KafkaManager) Service {
	ret := _m.Called(kafkaManager)

	if len(ret) == 0 {
		panic("no return value specified for WithKafkaManager")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(kcproc.KafkaManager) Service); ok {
		r0 = rf(kafkaManager)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithKafkaManager_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithKafkaManager'
type MockService_WithKafkaManager_Call struct {
	*mock.Call
}

// WithKafkaManager is a helper method to define mock.On call
//   - kafkaManager kcproc.KafkaManager
func (_e *MockService_Expecter) WithKafkaManager(kafkaManager interface{}) *MockService_WithKafkaManager_Call {
	return &MockService_WithKafkaManager_Call{Call: _e.mock.On("WithKafkaManager", kafkaManager)}
}

func (_c *MockService_WithKafkaManager_Call) Run(run func(kafkaManager kcproc.KafkaManager)) *MockService_WithKafkaManager_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(kcproc.KafkaManager))
	})
	return _c
}

func (_c *MockService_WithKafkaManager_Call) Return(_a0 Service) *MockService_WithKafkaManager_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithKafkaManager_Call) RunAndReturn(run func(kcproc.KafkaManager) Service) *MockService_WithKafkaManager_Call {
	_c.Call.Return(run)
	return _c
}

// WithMetricsService provides a mock function with given fields: _a0
func (_m *MockService) WithMetricsService(_a0 metrics.MetricsService) Service {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for WithMetricsService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(metrics.MetricsService) Service); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithMetricsService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithMetricsService'
type MockService_WithMetricsService_Call struct {
	*mock.Call
}

// WithMetricsService is a helper method to define mock.On call
//   - _a0 metrics.MetricsService
func (_e *MockService_Expecter) WithMetricsService(_a0 interface{}) *MockService_WithMetricsService_Call {
	return &MockService_WithMetricsService_Call{Call: _e.mock.On("WithMetricsService", _a0)}
}

func (_c *MockService_WithMetricsService_Call) Run(run func(_a0 metrics.MetricsService)) *MockService_WithMetricsService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(metrics.MetricsService))
	})
	return _c
}

func (_c *MockService_WithMetricsService_Call) Return(_a0 Service) *MockService_WithMetricsService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithMetricsService_Call) RunAndReturn(run func(metrics.MetricsService) Service) *MockService_WithMetricsService_Call {
	_c.Call.Return(run)
	return _c
}

// WithRepoService provides a mock function with given fields: repository
func (_m *MockService) WithRepoService(repository repo.Repository) Service {
	ret := _m.Called(repository)

	if len(ret) == 0 {
		panic("no return value specified for WithRepoService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(repo.Repository) Service); ok {
		r0 = rf(repository)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithRepoService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithRepoService'
type MockService_WithRepoService_Call struct {
	*mock.Call
}

// WithRepoService is a helper method to define mock.On call
//   - repository repo.Repository
func (_e *MockService_Expecter) WithRepoService(repository interface{}) *MockService_WithRepoService_Call {
	return &MockService_WithRepoService_Call{Call: _e.mock.On("WithRepoService", repository)}
}

func (_c *MockService_WithRepoService_Call) Run(run func(repository repo.Repository)) *MockService_WithRepoService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(repo.Repository))
	})
	return _c
}

func (_c *MockService_WithRepoService_Call) Return(_a0 Service) *MockService_WithRepoService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithRepoService_Call) RunAndReturn(run func(repo.Repository) Service) *MockService_WithRepoService_Call {
	_c.Call.Return(run)
	return _c
}

// WithSchedulerService provides a mock function with given fields: schedule
func (_m *MockService) WithSchedulerService(schedule scheduler.SchedulerService) Service {
	ret := _m.Called(schedule)

	if len(ret) == 0 {
		panic("no return value specified for WithSchedulerService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(scheduler.SchedulerService) Service); ok {
		r0 = rf(schedule)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithSchedulerService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithSchedulerService'
type MockService_WithSchedulerService_Call struct {
	*mock.Call
}

// WithSchedulerService is a helper method to define mock.On call
//   - schedule scheduler.SchedulerService
func (_e *MockService_Expecter) WithSchedulerService(schedule interface{}) *MockService_WithSchedulerService_Call {
	return &MockService_WithSchedulerService_Call{Call: _e.mock.On("WithSchedulerService", schedule)}
}

func (_c *MockService_WithSchedulerService_Call) Run(run func(schedule scheduler.SchedulerService)) *MockService_WithSchedulerService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(scheduler.SchedulerService))
	})
	return _c
}

func (_c *MockService_WithSchedulerService_Call) Return(_a0 Service) *MockService_WithSchedulerService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithSchedulerService_Call) RunAndReturn(run func(scheduler.SchedulerService) Service) *MockService_WithSchedulerService_Call {
	_c.Call.Return(run)
	return _c
}

// WithUsersService provides a mock function with given fields: usersService
func (_m *MockService) WithUsersService(usersService usr.UsersService) Service {
	ret := _m.Called(usersService)

	if len(ret) == 0 {
		panic("no return value specified for WithUsersService")
	}

	var r0 Service
	if rf, ok := ret.Get(0).(func(usr.UsersService) Service); ok {
		r0 = rf(usersService)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(Service)
		}
	}

	return r0
}

// MockService_WithUsersService_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WithUsersService'
type MockService_WithUsersService_Call struct {
	*mock.Call
}

// WithUsersService is a helper method to define mock.On call
//   - usersService usr.UsersService
func (_e *MockService_Expecter) WithUsersService(usersService interface{}) *MockService_WithUsersService_Call {
	return &MockService_WithUsersService_Call{Call: _e.mock.On("WithUsersService", usersService)}
}

func (_c *MockService_WithUsersService_Call) Run(run func(usersService usr.UsersService)) *MockService_WithUsersService_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(usr.UsersService))
	})
	return _c
}

func (_c *MockService_WithUsersService_Call) Return(_a0 Service) *MockService_WithUsersService_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockService_WithUsersService_Call) RunAndReturn(run func(usr.UsersService) Service) *MockService_WithUsersService_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
