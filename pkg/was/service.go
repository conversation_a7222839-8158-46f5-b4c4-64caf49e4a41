package was

import (
	"github.com/CyberOwlTeam/go-kafka-client/pkg/kcmodel"
	"github.com/CyberOwlTeam/go-kafka-client/pkg/kcproc"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/metrics"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/calc"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/inv"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/proc"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/scheduler"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/usr"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/was/warning"
	"github.com/pkg/errors"
	"github.com/samber/lo"
)

type Service interface {
	repo.Repository
	kcproc.KafkaManager
	inv.InvService
	metrics.MetricsService
	scheduler.SchedulerService
	warning.HistoryWarningService
	calc.CalculationsService
	usr.UsersService

	WithRepoService(repository repo.Repository) Service
	WithInventoryService(inventory inv.InvService) Service
	WithKafkaManager(kafkaManager kcproc.KafkaManager) Service
	WithMetricsService(metrics metrics.MetricsService) Service
	WithSchedulerService(schedule scheduler.SchedulerService) Service
	WithHistoryWarningService(historyWarningService warning.HistoryWarningService) Service
	WithCalculationService(calculationService calc.CalculationsService) Service
	WithUsersService(usersService usr.UsersService) Service
}

type service struct {
	cfg *modcfg.MainConfig
	repo.Repository
	kcproc.KafkaManager
	inv.InvService
	metrics.MetricsService
	scheduler.SchedulerService
	warning.HistoryWarningService
	calc.CalculationsService
	usr.UsersService
}

func NewService(cfg *modcfg.MainConfig) Service {
	s := &service{cfg: cfg}
	return s
}

func NewServiceWithAll(cfg *modcfg.MainConfig) (Service, error) {
	met := metrics.NewService(cfg)

	rs, err := repo.NewRepository(cfg)
	if err != nil {
		return nil, errors.Wrap(err, "failed initialising repository")
	}

	is, err := inv.NewService(cfg, rs)
	if err != nil {
		return nil, err
	}

	ss, err := scheduler.NewService(cfg, is, rs)
	if err != nil {
		return nil, err
	}

	errorAuditor := kcproc.NewErrorAuditor(make(chan *kcmodel.KafkaConsumerError))
	factory := proc.NewProcessorFactory(cfg, rs, errorAuditor)
	mode := lo.Ternary(cfg.Profiles.Runtime.IsDev() || cfg.Profiles.Runtime.IsDemo(), kcmodel.SafeAutoInitialiseMode, kcmodel.AutoInitialiseMode)
	km, err := kcproc.NewKafkaManager(cfg.Processors, factory, mode)
	if err != nil {
		return nil, errors.Wrap(err, "failed initialising kafka kafkaManager")
	}

	timeSource := csource.NewTimeSource()
	uuidSource := csource.NewUuidSource()

	historyWarningService := warning.NewService(*cfg, rs, timeSource, uuidSource)

	calculationsService := calc.NewService(*cfg, rs, timeSource, uuidSource)

	usersService, err := usr.NewUsersService(*cfg, rs, timeSource, uuidSource)
	if err != nil {
		return nil, errors.Wrap(err, "failed initialising users service")
	}

	return NewService(cfg).
			WithRepoService(rs).
			WithInventoryService(is).
			WithKafkaManager(km).
			WithMetricsService(met).
			WithSchedulerService(ss).
			WithHistoryWarningService(historyWarningService).
			WithCalculationService(calculationsService).
			WithUsersService(usersService),
		nil
}

func (s *service) WithRepoService(repoService repo.Repository) Service {
	s.Repository = repoService
	return s
}

func (s *service) WithInventoryService(inventoryService inv.InvService) Service {
	s.InvService = inventoryService
	return s
}

func (s *service) WithKafkaManager(kafkaManager kcproc.KafkaManager) Service {
	s.KafkaManager = kafkaManager
	return s
}

func (s *service) WithMetricsService(metricsService metrics.MetricsService) Service {
	s.MetricsService = metricsService
	return s
}

func (s *service) WithSchedulerService(schedulerService scheduler.SchedulerService) Service {
	s.SchedulerService = schedulerService
	return s
}

func (s *service) WithHistoryWarningService(historyWarningService warning.HistoryWarningService) Service {
	s.HistoryWarningService = historyWarningService
	return s
}

func (s *service) WithCalculationService(calculationService calc.CalculationsService) Service {
	s.CalculationsService = calculationService
	return s
}

func (s *service) WithUsersService(usersService usr.UsersService) Service {
	s.UsersService = usersService
	return s
}
