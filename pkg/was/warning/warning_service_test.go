package warning

import (
	"testing"

	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWarningService_FindNotificationRuleMatch(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Use mock users
	user1 := modusr.MockUser1(t)
	user2 := modusr.MockUser2(t)
	users := []modusr.User{*user1, *user2}

	// Use mock asset
	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	// Use mock notification rule
	matchingRule := modnotif.MockNotificationRule1(t)
	matchingRule.Source = "source1"
	matchingRule.EventType = "event_type1"
	matchingRule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Use mock notification setting
	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*matchingRule}

	// Setup mock repository expectations
	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, userId, err := service.FindNotificationRuleMatch(*warning)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, result)
	require.NotNil(t, userId)
	assert.Equal(t, matchingRule.Identity, result.Identity)
	assert.Equal(t, matchingRule.Source, result.Source)
	assert.Equal(t, matchingRule.EventType, result.EventType)
	assert.Equal(t, user1.Identity, *userId)
}

func TestWarningService_FindNotificationRuleMatch_NoMatch(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Use mock users
	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	// Use mock asset
	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	// Use mock notification rule with non-matching source
	nonMatchingRule := modnotif.MockNotificationRule2(t)
	nonMatchingRule.Source = "different_source" // Different source
	nonMatchingRule.EventType = "event_type1"
	nonMatchingRule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Use mock notification setting
	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*nonMatchingRule}

	// Setup mock repository expectations
	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, userId, err := service.FindNotificationRuleMatch(*warning)

	// Assertions
	require.NoError(t, err)
	require.Nil(t, result)
}

func TestWarningService_FindNotificationRuleMatch_NoAssetId(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning without an assetId in the Code field
	warning := modwarn.MockWarning1(t)
	warning.Code = cjson.JSONB{"someOtherField": "value"} // No assetId
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Use mock users
	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	// Use mock notification rule
	rule := modnotif.MockNotificationRule1(t)
	rule.Source = "source1"
	rule.EventType = "event_type1"
	rule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: "some-lookup",
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Use mock notification setting
	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*rule}

	// Setup mock repository expectations
	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, userId, err := service.FindNotificationRuleMatch(*warning)

	// Assertions
	require.NoError(t, err)
	require.Nil(t, result)
}

func TestWarningService_FindNotificationRuleMatch_NoNotificationRules(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Use mock users
	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	// Use mock notification setting with no rules
	notificationSetting := modnotif.MockNotificationSetting3(t) // This mock has empty rules

	// Setup mock repository expectations
	mockRepo.EXPECT().GetAllUsers().Return(users, nil)
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(notificationSetting, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, userId, err := service.FindNotificationRuleMatch(*warning)

	// Assertions
	require.NoError(t, err)
	require.Nil(t, result)
}

func TestWarningService_matchNotificationRule(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Use mock user
	user := modusr.MockUser1(t)

	// Use mock asset
	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	// Use mock notification rule
	matchingRule := modnotif.MockNotificationRule1(t)
	matchingRule.Source = "source1"
	matchingRule.EventType = "event_type1"
	matchingRule.AffectedItems = []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Use mock notification setting
	notificationSetting := modnotif.MockNotificationSetting1(t)
	notificationSetting.NotificationRules = []modnotif.NotificationRule{*matchingRule}

	// Setup mock repository expectations
	mockRepo.EXPECT().GetNotificationSettingByUserId(user.Identity).Return(notificationSetting, nil)
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, err := service.matchNotificationRule(*warning, *user)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, result)
	assert.Equal(t, matchingRule.Identity, result.Identity)
}

func TestWarningService_matchAffectedItems(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()

	// Use mock asset
	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	// Create affected items for different lookup types
	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: asset.Name,
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Setup mock repository expectations
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result := service.matchAffectedItems(affectedItems, *warning)

	// Assertions
	assert.True(t, result)
}

func TestWarningService_FindNotificationRuleMatch_ErrorGettingUsers(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning
	warning := modwarn.MockWarning1(t)
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Setup mock repository to return an error
	expectedError := assert.AnError
	mockRepo.EXPECT().GetAllUsers().Return(nil, expectedError)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, userId, err := service.FindNotificationRuleMatch(*warning)

	// Assertions
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Nil(t, userId)
}

func TestWarningService_FindNotificationRuleMatch_ErrorGettingNotificationSetting(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning
	warning := modwarn.MockWarning1(t)
	warning.Source = "source1"
	warning.EventType = "event_type1"

	// Use mock users
	user1 := modusr.MockUser1(t)
	users := []modusr.User{*user1}

	// Setup mock repository expectations
	mockRepo.EXPECT().GetAllUsers().Return(users, nil)

	expectedError := assert.AnError
	mockRepo.EXPECT().GetNotificationSettingByUserId(user1.Identity).Return(nil, expectedError)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result, userId, err := service.FindNotificationRuleMatch(*warning)

	// Assertions
	require.Error(t, err)
	require.Equal(t, expectedError, err)
	require.Nil(t, result)
	require.Nil(t, userId)
}

func TestWarningService_matchAffectedItems_NoAssetId(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning without an assetId in the Code field
	warning := modwarn.MockWarning1(t)
	warning.Code = cjson.JSONB{"someOtherField": "value"} // No assetId

	// Create affected items
	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: "some-lookup",
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result := service.matchAffectedItems(affectedItems, *warning)

	// Assertions
	assert.False(t, result)
}

func TestWarningService_matchAffectedItems_InvalidAssetId(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning with an invalid assetId in the Code field
	warning := modwarn.MockWarning1(t)
	warning.Code = cjson.JSONB{"assetId": "not-a-uuid"} // Invalid UUID

	// Create affected items
	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: "some-lookup",
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result := service.matchAffectedItems(affectedItems, *warning)

	// Assertions
	assert.False(t, result)
}

func TestWarningService_matchAffectedItems_ErrorRetrievingAsset(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()

	// Create affected items
	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: "some-lookup",
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Setup mock repository to return an error
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(nil, assert.AnError)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result := service.matchAffectedItems(affectedItems, *warning)

	// Assertions
	assert.False(t, result)
}

func TestWarningService_matchAffectedItems_NoMatchingLookup(t *testing.T) {
	// Setup
	mockRepo := repo.NewMockRepository(t)
	mockConfig := modcfg.MockMainConfig(t)

	// Use mock warning and add assetId to its Code field
	warning := modwarn.MockWarning1(t)
	assetId := modinv.MockAsset1(t).Identity
	warning.Code["assetId"] = assetId.String()

	// Use mock asset
	asset := modinv.MockAsset1(t)
	asset.LocationIdentity = warning.LocationId

	// Create affected items with non-matching lookup
	affectedItems := []modnotif.AffectedItem{
		{
			Lookup: "Different Name", // Different from asset.Name
			Type:   modnotif.LookupTypeAssetName,
		},
	}

	// Setup mock repository expectations
	mockRepo.EXPECT().RetrieveAsset(assetId).Return(asset, nil)

	// Create the service
	service := &warningService{
		cfg:        *mockConfig,
		repo:       mockRepo,
		timeSource: csource.NewTimeSource(),
		uuidSource: csource.NewUuidSource(),
	}

	// Test
	result := service.matchAffectedItems(affectedItems, *warning)

	// Assertions
	assert.False(t, result)
}
