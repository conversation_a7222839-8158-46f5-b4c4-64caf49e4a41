package warning

import (
	"encoding/json"
	"github.com/CyberOwlTeam/go-kafka-client/pkg/kcmodel"
	"github.com/CyberOwlTeam/go-kafka-client/pkg/kcproc"
	"github.com/CyberOwlTeam/go-kafka-client/pkg/kcservices"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/repo"
	"github.com/google/uuid"
)

type warningsProcessor struct {
	kafkaProcessor kcproc.KafkaProcessor[modwarn.WarningDefinition]
	repo           repo.Repository
	service        WarningService
}

func NewWarningsProcessor(
	cfg *modcfg.MainConfig,
	signalsChan chan kcmodel.KafkaConsumerSignal,
	errorsChan chan *kcmodel.KafkaConsumerError,
	spec kcmodel.KafkaProcessorSpec,
	repository repo.Repository,
	cwc ...kcservices.KafkaConsumerWithCallback[modwarn.WarningDefinition],
) (kcproc.KafkaProcessor[modwarn.WarningDefinition], error) {
	kafkaConfig, topicConfig, err := cfg.Kafka.GetKafkaConfigIfEnabled(spec.TopicLookup)
	if err != nil {
		return nil, err
	} else if topicConfig == nil {
		return nil, nil
	}

	if spec.GenerateConsumerGroupId {
		consumerGroupId := uuid.New().String()
		kafkaConfig.GroupID = &consumerGroupId
	}

	timeSource := csource.NewTimeSource()
	uuidSource := csource.NewUuidSource()

	s := NewService(*cfg, repository, timeSource, uuidSource)

	p := &warningsProcessor{
		repo:    repository,
		service: s,
	}

	var consumer kcservices.KafkaConsumerWithCallback[modwarn.WarningDefinition]
	if len(cwc) > 0 {
		consumer = cwc[0]
	} else {
		consumer, err = kcservices.NewKafkaConsumerWithCallback(kafkaConfig, topicConfig, p.process)
		if err != nil {
			return nil, err
		}
		consumer.
			WithCallBackUnmarshaller(cjson.Unmarshaler).
			WithErrorsChannel(errorsChan).
			WithSignalsChannel(signalsChan)
	}

	processor := kcproc.NewKafkaProcessor(
		kcmodel.NewKafkaProcessorDetails(spec, signalsChan, errorsChan),
		kcservices.NewKafkaConsumerAndProducer(consumer),
	)
	p.kafkaProcessor = processor

	return processor, nil
}

func (p *warningsProcessor) process(warningDefinition *modwarn.WarningDefinition, _ json.RawMessage) (any, error) {
	return nil, p.service.ProcessWarning(*warningDefinition)
}
