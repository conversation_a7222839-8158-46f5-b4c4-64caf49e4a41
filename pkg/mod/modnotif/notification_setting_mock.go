//go:build !compile

package modnotif

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"testing"
)

func MockNotificationSetting1(t testing.TB) *NotificationSetting {
	return &NotificationSetting{
		Identity:          csource.MockUuid1,
		Name:              "notif setting 1",
		Description:       "some description",
		IsPredefined:      false,
		NotificationRules: []NotificationRule{*MockNotificationRule1(t)},
		Users:             nil,
	}
}

func MockNotificationSetting2(t testing.TB) *NotificationSetting {
	return &NotificationSetting{
		Identity:          csource.MockUuid2,
		Name:              "notif setting 2",
		Description:       "some description 2",
		IsPredefined:      false,
		NotificationRules: []NotificationRule{*MockNotificationRule2(t)},
		Users:             []NotificationSettingsToUser{*MockNotificationSettingsToUser1(t)},
	}
}

func MockNotificationSetting3(t testing.TB) *NotificationSetting {
	return &NotificationSetting{
		Identity:          csource.MockUuid3,
		Name:              "notif setting 3",
		Description:       "some description 3",
		IsPredefined:      false,
		NotificationRules: []NotificationRule{},
		Users:             nil,
	}
}

func MockNotificationSetting4(t testing.TB) *NotificationSetting {
	return &NotificationSetting{
		Identity:          csource.MockUuid4,
		Name:              "notif setting 4",
		Description:       "some description 4",
		IsPredefined:      false,
		NotificationRules: []NotificationRule{},
		Users:             nil,
	}
}

func AllMockNotificationSettings(t testing.TB) []NotificationSetting {
	return []NotificationSetting{
		*MockNotificationSetting1(t),
		*MockNotificationSetting2(t),
	}
}
