package modnotif

import (
	"github.com/google/uuid"
)

type NotificationSettingsToUser struct {
	NotificationSettingId    uuid.UUID `json:"notificationSettingId" gorm:"column:notification_setting_id;primaryKey"`
	UserId                   uuid.UUID `json:"userId" gorm:"column:user_id;primaryKey"`
	IsAdditionalEmailAddress bool      `json:"isAdditionalEmailAddress" gorm:"column:is_additional_email_address"`
	GroupNotifications       bool      `json:"groupNotifications" gorm:"column:group_notifications"`
	AdditionalEmailAddress   string    `json:"additionalEmailAddress" gorm:"column:additional_email_address"`
}

func (nstu *NotificationSettingsToUser) TableName() string {
	return "notification_settings2users"
}
