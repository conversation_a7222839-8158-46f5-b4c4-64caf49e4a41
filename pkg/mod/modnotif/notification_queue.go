package modnotif

import (
	"github.com/google/uuid"
)

type NotificationQueue struct {
	UserId             uuid.UUID             `json:"userId" gorm:"column:user_id;primaryKey"`
	NotificationRuleId uuid.UUID             `json:"notificationRuleId" gorm:"column:notification_rule_id;primaryKey"`
	WarningId          uuid.UUID             `json:"warningId" gorm:"column:warning_id;primaryKey"`
	Frequency          NotificationFrequency `json:"frequency" gorm:"column:frequency"`
}

type NotificationQueueIdentity struct {
	UserId             uuid.UUID `json:"userId"`
	NotificationRuleId uuid.UUID `json:"notificationRuleId"`
	WarningId          uuid.UUID `json:"warningId"`
}

func (nq *NotificationQueue) GetIdentity() NotificationQueueIdentity {
	return NotificationQueueIdentity{
		UserId:             nq.UserId,
		NotificationRuleId: nq.NotificationRuleId,
		WarningId:          nq.WarningId,
	}
}

// TableName specifies the database table name for GORM
func (nq *NotificationQueue) TableName() string {
	return "notification_queues"
}
