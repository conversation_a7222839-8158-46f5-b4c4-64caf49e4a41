package modnotif

import (
	"github.com/google/uuid"
)

type NotificationSetting struct {
	Identity          uuid.UUID                    `json:"identity" gorm:"column:id;primaryKey;default:uuid_generate_v4()"`
	Name              string                       `json:"name" gorm:"column:name"`
	Description       string                       `json:"description" gorm:"column:description"`
	IsPredefined      bool                         `json:"isPredefined" gorm:"column:is_predefined"`
	NotificationRules []NotificationRule           `json:"notificationRules" gorm:"foreignKey:notification_setting_id;references:id"`
	Users             []NotificationSettingsToUser `json:"users" gorm:"foreignKey:notification_setting_id;references:id"`
}

func (ns *NotificationSetting) TableName() string {
	return "notification_settings"
}

type NotificationSettingDefinition struct {
	Name         string `json:"name"`
	Description  string `json:"description"`
	IsPredefined bool   `json:"isPredefined"`
}

func NewNotificationSettingFromDefinition(definition NotificationSettingDefinition) NotificationSetting {
	return NotificationSetting{
		Identity:     uuid.New(),
		Name:         definition.Name,
		Description:  definition.Description,
		IsPredefined: definition.IsPredefined,
	}
}
