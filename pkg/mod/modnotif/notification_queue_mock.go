//go:build !compile

package modnotif

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/ctutils"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/csource"
	"testing"
)

func MockNotificationQueue1(t testing.TB) *NotificationQueue {
	return &NotificationQueue{
		UserId:             *ctutils.MustParseUuid(t, "101dda18-55e1-4b65-966d-c6f9fabdebb9"),
		NotificationRuleId: csource.MockUuid3,
		WarningId:          *ctutils.MustParseUuid(t, "1017c53f-c5b7-4479-9e8b-32002b4d196a"),
		Frequency:          NotificationFrequencyDaily,
	}
}

func MockNotificationQueue2(t testing.TB) *NotificationQueue {
	return &NotificationQueue{
		UserId:             *ctutils.MustParseUuid(t, "102dda18-55e1-4b65-966d-c6f9fabdebb9"),
		NotificationRuleId: csource.MockUuid4,
		WarningId:          *ctutils.MustParseUuid(t, "1027c53f-c5b7-4479-9e8b-32002b4d196a"),
		Frequency:          NotificationFrequencyWeekly,
	}
}

func AllMockNotificationQueues(t testing.TB) []NotificationQueue {
	return []NotificationQueue{
		*MockNotificationQueue1(t),
		*MockNotificationQueue2(t),
	}
}
