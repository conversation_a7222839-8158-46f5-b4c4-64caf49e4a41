package modnotif

import (
	"github.com/google/uuid"
)

type LookupType string

const (
	LookupTypeLocationId LookupType = "locationId"
	LookupTypeAssetId    LookupType = "assetId"
	LookupTypeAssetName  LookupType = "assetName"
)

type AffectedItem struct {
	Lookup             string     `json:"lookup" gorm:"column:lookup;primaryKey"`
	NotificationRuleId uuid.UUID  `json:"notificationRuleId" gorm:"column:notification_rule_id;primaryKey"`
	Type               LookupType `json:"type" gorm:"column:type;primaryKey"`
}

func (ai *AffectedItem) TableName() string {
	return "affected_items"
}
