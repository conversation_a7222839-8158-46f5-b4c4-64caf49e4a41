package modwarn

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestWarning_GetAssetId(t *testing.T) {
	tests := []struct {
		name        string
		code        cjson.JSONB
		expected    *string
		expectError bool
	}{
		{
			name:        "valid assetId",
			code:        cjson.JSONB{"assetId": "90d02110-cff2-4572-9609-58cf23d75a7e", "metricType": "malwareDetection"},
			expected:    lo.ToPtr("90d02110-cff2-4572-9609-58cf23d75a7e"),
			expectError: false,
		},
		{
			name:        "missing assetId",
			code:        cjson.JSONB{"metricType": "malwareDetection"},
			expected:    nil,
			expectError: true,
		},
		{
			name:        "invalid assetId",
			code:        cjson.JSONB{"assetId": 123, "metricType": "malwareDetection"},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			warning := Warning{
				Code: tt.code,
			}

			result, err := warning.GetAssetId()

			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}

			assert.Equal(t, tt.expected, result)
		})
	}
}
