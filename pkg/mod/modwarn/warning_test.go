package modwarn

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestWarning_GetAssetId(t *testing.T) {
	tests := []struct {
		name     string
		code     cjson.JSONB
		expected *string
		expectOk bool
	}{
		{
			name:     "valid assetId",
			code:     cjson.JSONB{"asset": "90d02110-cff2-4572-9609-58cf23d75a7e", "metricType": "malwareDetection"},
			expected: lo.ToPtr("90d02110-cff2-4572-9609-58cf23d75a7e"),
			expectOk: true,
		},
		{
			name:     "missing assetId",
			code:     cjson.JSONB{"metricType": "malwareDetection"},
			expected: nil,
			expectOk: false,
		},
		{
			name:     "invalid assetId",
			code:     cjson.JSONB{"asset": 123, "metricType": "malwareDetection"},
			expected: nil,
			expectOk: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			warning := Warning{
				Code: tt.code,
			}

			result, ok := warning.GetAssetId()

			if tt.expectOk {
				assert.True(t, ok)
			} else {
				assert.False(t, ok)
			}

			assert.Equal(t, tt.expected, result)
		})
	}
}
