package modwarn

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"time"
)

type WarningType string

const (
	WarningTypeOpenEnded  WarningType = "openEnded"
	WarningTypeClosedLoop WarningType = "closedLoop"
)

type WarningState string

const (
	WarningStateActive    WarningState = "active"
	WarningStateMuted     WarningState = "muted"
	WarningStateDormant   WarningState = "dormant"
	WarningStateDismissed WarningState = "dismissed"
	WarningStateExpired   WarningState = "expired"
)

type Warning struct {
	Identity       uuid.UUID        `json:"identity" gorm:"column:id;primaryKey;default:uuid_generate_v4()"`
	Code           cjson.JSONB      `json:"code" gorm:"column:code"`
	Type           WarningType      `json:"type" gorm:"column:type"`
	State          WarningState     `json:"state" gorm:"column:state"`
	Risk           float64          `json:"risk" gorm:"column:risk"`
	Source         string           `json:"source" gorm:"column:source"`
	EventType      string           `json:"eventType" gorm:"column:event_type"`
	Title          string           `json:"title" gorm:"column:title"`
	LocationId     uuid.UUID        `json:"location" gorm:"column:location_id"`
	WarningHistory []WarningHistory `json:"warningHistory" gorm:"foreignKey:warning_id;references:id"`
	LastSeen       time.Time        `json:"lastSeen" gorm:"column:last_seen"`
	CreatedAt      time.Time        `json:"createdAt" gorm:"column:created_at"`
	UpdatedAt      time.Time        `json:"updatedAt" gorm:"column:updated_at"`
	Number         string           `json:"number" gorm:"column:number;->"` // -> indicates this will ALWAYS be omitted from inserts and updates
}

func (wh *Warning) TableName() string {
	return "warnings"
}

func (wh *Warning) GetAssetId() (*string, error) {
	if wh.Code == nil {
		return nil, errors.New("warning code is nil")
	}

	assetId, ok := wh.Code["assetId"]
	if !ok {
		return nil, errors.New("assetId not found in warning code")
	}

	assetUuid, ok := assetId.(string)
	if !ok {
		return nil, errors.Errorf("assetId is not a string: %v", assetId)
	}

	return &assetUuid, nil
}

func NewWarningFromDefinition(definition WarningDefinition, stateToUse *WarningState) Warning {
	initialState := WarningStateActive
	if stateToUse != nil {
		initialState = *stateToUse
	}

	now := time.Now()

	return Warning{
		Identity:       uuid.New(),
		Code:           definition.Code,
		Type:           definition.Type,
		State:          initialState,
		Risk:           definition.Risk,
		Source:         definition.Source,
		Title:          definition.Title,
		LocationId:     definition.LocationId,
		WarningHistory: nil,
		LastSeen:       now,
		CreatedAt:      now,
		UpdatedAt:      now,
		EventType:      definition.EventType,
	}
}
