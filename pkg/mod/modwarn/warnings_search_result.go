package modwarn

import "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod"

type WarningSearchResult struct {
	Data               []Warning             `json:"data"`
	Criteria           WarningSearchCriteria `json:"criteria"`
	TotalNumberOfItems int                   `json:"totalNumberOfItems"`
	TotalNumberOfPages int                   `json:"totalNumberOfPages"`
}

func NewWarningsSearchResult(data []Warning, criteria WarningSearchCriteria, totalNumberOfItems int) *WarningSearchResult {
	return &WarningSearchResult{
		Data:               data,
		Criteria:           criteria,
		TotalNumberOfItems: totalNumberOfItems,
		TotalNumberOfPages: calculateTotalNumberOfPages(criteria.Pagination, totalNumberOfItems),
	}
}

func calculateTotalNumberOfPages(pagination *mod.SearchCriteriaPagination, numberOfItems int) int {
	if pagination == nil {
		pagination = mod.NewDefaultSearchCriteriaPagination()
	}

	pageSize := *pagination.PageSize

	div := numberOfItems / pageSize
	modulus := numberOfItems % pageSize
	if modulus > 0 {
		return div + 1
	}
	return div
}
