package mod

import (
	"github.com/samber/lo"
)

const (
	DefaultPageSize   = 100
	DefaultPageOffset = 0
)

type SearchCriteriaPagination struct {
	PageSize   *int `json:"pageSize" query:"pageSize"`
	PageOffset *int `json:"pageOffset" query:"pageOffset"` // pagination starts @ ZERO
}

func NewDefaultSearchCriteriaPagination() *SearchCriteriaPagination {
	s := DefaultPageSize
	o := DefaultPageOffset

	return &SearchCriteriaPagination{
		PageSize:   &s,
		PageOffset: &o,
	}
}

func NewSearchCriteriaPagination(s *int, o *int) *SearchCriteriaPagination {
	if s == nil && o == nil {
		return NewDefaultSearchCriteriaPagination()
	}
	if s == nil {
		s = lo.ToPtr(DefaultPageSize)
	} else if *s == -1 {
		s = nil
	}
	if o == nil {
		o = lo.ToPtr(DefaultPageOffset)
	} else if *o == -1 {
		o = nil
	}

	return &SearchCriteriaPagination{
		PageSize:   s,
		PageOffset: o,
	}
}

func NewSearchCriteriaPaginationForDeepLink(s int, o int) *SearchCriteriaPagination {
	return &SearchCriteriaPagination{
		PageSize:   lo.ToPtr(s),
		PageOffset: lo.ToPtr(lo.Ternary(o > 0, o-1, 0)),
	}
}
