package warn

import (
	"fmt"
	"github.com/pkg/errors"
	"time"
)

type EventItem struct {
	Name           string         `json:"name"`
	DisplayName    string         `json:"displayName"`
	Recommendation string         `json:"recommendation"`
	MaxScore       float64        `json:"maxScore"`
	DormancyPeriod *time.Duration `json:"dormancyPeriod,omitempty"`
}

type SourceConfig struct {
	DisplayName  string      `json:"displayName"`
	MaxScore     float64     `json:"maxScore"`
	EventConfigs []EventItem `json:"eventConfigs"`
}

type WarningConfigs map[string]SourceConfig

func (w WarningConfigs) Validate() error {
	var invalid []string

	for source, sourceConfig := range w {
		if source == "" {
			invalid = append(invalid, "empty warning source")
		}
		if sourceConfig.MaxScore < 0 {
			invalid = append(invalid, fmt.Sprintf("WarningsConfig[%q][%v]: MaxScore must be >= 0", source, source))
		}
		if sourceConfig.DisplayName == "" {
			invalid = append(invalid, "DisplayName")
		}
		for idx, eventConfig := range sourceConfig.EventConfigs {
			if eventConfig.Name == "" {
				invalid = append(invalid, fmt.Sprintf("WarningsConfig[%q][%d]: empty Name", source, idx))
			}
			if eventConfig.DisplayName == "" {
				invalid = append(invalid, fmt.Sprintf("WarningsConfig[%q][%d]: empty DisplayName", source, idx))
			}
			if eventConfig.MaxScore < 0 {
				invalid = append(invalid, fmt.Sprintf("WarningsConfig[%q][%d]: MaxScore must be >= 0", source, idx))
			}
			if eventConfig.Recommendation == "" {
				invalid = append(invalid, fmt.Sprintf("WarningsConfig[%q][%d]: Recommendation must be non-empty", source, idx))
			}
		}
	}

	if len(invalid) > 0 {
		return errors.Errorf("WarningsConfig validation errors: %v", invalid)
	}
	return nil
}
