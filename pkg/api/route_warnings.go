package api

import (
	"fmt"
	"github.com/CyberOwlTeam/go-authentication/pkg/authmod"
	"github.com/CyberOwlTeam/go-authentication/pkg/authsvc"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	"github.com/CyberOwlTeam/go-utilities/pkg/cutils/clog"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcalc"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/CyberOwlTeam/go-web-api/pkg/wapi"
	"github.com/CyberOwlTeam/go-web-api/pkg/wapimod"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/samber/lo"
)

func (s *service) getAllWarningsRoutes() []wapi.FSetupRoute {
	return []wapi.FSetupRoute{
		s.setupGetAllWarnings,
		s.setupCreateWarning,
		s.setupRetrieveWarning,
		s.setupGetWarningForCode,
		s.setupUpdateWarningState,
		s.setupFindWarnings,
		s.setupFindWarningsDeepLink,
		s.setupUpdateWarning,
		s.setupDeleteWarning,
		s.setupCalculateScores,
		s.setupGetWarningConfig,
	}
}

// setupGetWarningConfig godoc
// @Security ApiKeyAuth
// @Summary Get all the current warning configs
// @Description Will return the warning configs defined in the yaml
// @Tags warnings
// @Produce  json
// @Success 200 {object} warn.WarningConfigs
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings/config/warningsConfig [get]
func (s *service) setupGetWarningConfig(app *fiber.App) {
	app.Get(
		s.BuildPath("/warnings/config/warningsConfig"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
			wapimod.CustomerAdminUserRole,
			wapimod.UserUserRole,
		}),
		s.GetWarningConfig,
	)
}

func (s *service) GetWarningConfig(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("GetWarningConfig")
	defer clog.OnExit("GetWarningConfig", err)

	return ctx.Status(fiber.StatusOK).JSON(s.mainConfig.Warnings)
}

// CalculateWarningScores godoc
// @Security ApiKeyAuth
// @Summary Calculate the score of all warnings per category
// @Description Calculate the score of all warnings per category
// @Tags warnings
// @Produce  json
// @Success 200 {json} map[uuid.UUID][]modcalc.CalculationPerCategory
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Param code body modcalc.Criteria true "the criteria of the warnings"
// @Router /warnings/scores [post]
func (s *service) setupCalculateScores(app *fiber.App) {
	app.Post(
		s.BuildPath("/warnings/scores"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
			wapimod.CustomerAdminUserRole,
			wapimod.UserUserRole,
		}),
		s.GetStrictAuthorizationHandler(s.criteriaLocationsExtractor(func() mod.LocationSearchCriteria {
			return modcalc.Criteria{}
		})),
		s.CalculateWarningScores,
	)
}

func (s *service) CalculateWarningScores(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("CalculateWarningScores")
	defer clog.OnExit("CalculateWarningScores", err)

	var criteria = modcalc.Criteria{}
	err = ctx.BodyParser(&criteria)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid criteria", err))
	}

	if userLocations := ctx.Locals(authsvc.UserLocationsLookup); userLocations != nil && criteria.Locations == nil {
		criteria.Locations = userLocations.([]uuid.UUID)
	}

	result, err := s.local.CalculateLocationScores(criteria)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("an error occurred calculating the warnings", err))
	} else {
		return ctx.Status(fiber.StatusOK).JSON(result)
	}
}

// GetAllWarnings godoc
// @Security ApiKeyAuth
// @Summary get an array of all warnings
// @Description get a full array of all warnings
// @Tags warnings
// @Produce  json
// @Success 200 {json} []modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Param includeInactive query bool false "If true, inactive warnings will be included. defaults to false"
// @Router /warnings [get]
func (s *service) setupGetAllWarnings(app *fiber.App) {
	app.Get(
		s.BuildPath("/warnings"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
		}),
		s.getAllWarnings,
	)
}

func (s *service) getAllWarnings(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("getAllWarnings")
	defer clog.OnExit("getAllWarnings", err)

	results, err := s.local.GetAllWarnings()
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("an error occurred getting all warnings", err))
	} else if results == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError("failed to get all warnings", err))
	} else {
		return ctx.Status(fiber.StatusOK).JSON(results)
	}
}

// RetrieveWarning godoc
// @Security ApiKeyAuth
// @Summary get a single warning
// @Description get a single warning from ID
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings/{identity} [get]
// @Param identity path string true "Identity of the warning"
func (s *service) setupRetrieveWarning(app *fiber.App) {
	app.Get(
		s.BuildPath("/warnings/:identity"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
			wapimod.CustomerAdminUserRole,
			wapimod.UserUserRole,
		}),
		s.retrieveWarning,
	)
}

func (s *service) retrieveWarning(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("retrieveWarning")
	defer clog.OnExit("retrieveWarning", err)

	_identity := ctx.Params("identity")
	if _identity == "" {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("no identity specified", err))
	}
	identity, err := uuid.Parse(_identity)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred parsing the identity: %s", _identity), err))
	}

	result, err := s.local.RetrieveWarning(identity)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred retrieving warning: %v", identity), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to retrieve with: %v", identity), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// GetWarningForCode godoc
// @Security ApiKeyAuth
// @Summary get a single warning for code
// @Description get a single warning for code
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warningsCode [post]
// @Param code body cjson.JSONB true "code of the warning"
func (s *service) setupGetWarningForCode(app *fiber.App) {
	app.Post(
		s.BuildPath("/warningsCode"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
			wapimod.CustomerAdminUserRole,
			wapimod.UserUserRole,
		}),
		s.getWarningForCode,
	)
}

func (s *service) getWarningForCode(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("getWarningForCode")
	defer clog.OnExit("getWarningForCode", err)

	var code = cjson.JSONB{}
	err = ctx.BodyParser(&code)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid warning", err))
	}

	result, err := s.local.GetWarningForCode(code)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred getting warning for code: %v", code), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to warning for code: %v", code), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// UpdateWarningState godoc
// @Security ApiKeyAuth
// @Summary update warning state
// @Description update warning state
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings/{identity}/state/{state} [get]
// @Param identity path string true "Identity of the warning"
// @Param state path string true "state"
func (s *service) setupUpdateWarningState(app *fiber.App) {
	app.Get(
		s.BuildPath("/warnings/:identity/state/:state"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
		}),
		s.updateWarningState,
	)
}

func (s *service) updateWarningState(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("updateWarningState")
	defer clog.OnExit("updateWarningState", err)

	_identity := ctx.Params("identity")
	if _identity == "" {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("no identity specified", err))
	}
	identity, err := uuid.Parse(_identity)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred parsing the identity: %s", _identity), err))
	}

	state := modwarn.WarningState(ctx.Params("state"))
	if state == "" {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("no state specified", err))
	}

	result, err := s.local.UpdateWarningState(identity, state)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred retrieving warning: %v", identity), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to retrieve with: %v", identity), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// CreateWarning godoc
// @Security ApiKeyAuth
// @Summary Create a warning
// @Description Create a warning
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings [post]
// @Param warning body modwarn.Warning true "the warning to create"
func (s *service) setupCreateWarning(app *fiber.App) {
	app.Post(
		s.BuildPath("/warnings"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
		}),
		s.createWarning,
	)
}

func (s *service) createWarning(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("createWarning")
	defer clog.OnExit("createWarning", err)

	var warning = modwarn.Warning{}
	err = ctx.BodyParser(&warning)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid warning specified", err))
	}

	result, err := s.local.CreateWarning(warning)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred creating the warning: %v", warning), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to create warning with: %v", warning), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// UpdateWarning godoc
// @Security ApiKeyAuth
// @Summary Update a warning
// @Description update a warning
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings [put]
// @Param warning body modwarn.Warning true "the warning to update"
func (s *service) setupUpdateWarning(app *fiber.App) {
	app.Put(
		s.BuildPath("/warnings"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
		}),
		s.updateWarning,
	)
}

func (s *service) updateWarning(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("updateWarning")
	defer clog.OnExit("updateWarning", err)

	var warning = modwarn.Warning{}
	err = ctx.BodyParser(&warning)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid warning", err))
	}

	result, err := s.local.UpdateWarning(warning)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred updating warning: %v", warning), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to update warning with: %v", warning), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// FindWarnings godoc
// @Security ApiKeyAuth
// @Summary Find warnings
// @Description Find warnings
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings/find [post]
// @Param warning body modwarn.WarningSearchCriteria true "the warning to update"
func (s *service) setupFindWarnings(app *fiber.App) {
	app.Post(
		s.BuildPath("/warnings/find"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
			wapimod.CustomerAdminUserRole,
			wapimod.UserUserRole,
		}),
		s.GetStrictAuthorizationHandler(s.criteriaLocationsExtractor(func() mod.LocationSearchCriteria {
			return modwarn.NewDefaultWarningSearchCriteria()
		})),
		s.findWarnings,
	)
}

func (s *service) findWarnings(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("findWarnings")
	defer clog.OnExit("findWarnings", err)

	var criteria = modwarn.NewDefaultWarningSearchCriteria()
	err = ctx.BodyParser(&criteria)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid criteria", err))
	}

	if userLocations := ctx.Locals(authsvc.UserLocationsLookup); userLocations != nil && criteria.Locations == nil {
		criteria.Locations = userLocations.([]uuid.UUID)
	}

	result, err := s.local.FindWarnings(*criteria, nil)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred finding warnings: %v", criteria), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to find warnings with: %v", criteria), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// FindWarnings godoc
// @Security ApiKeyAuth
// @Summary Find warnings
// @Description Find warnings
// @Tags warnings
// @Produce json
// @Success 200 {json} modwarn.Warning
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings/{identity}/find [post]
// @Param identity path string true "Identity of the warning"
// @Param warning body modwarn.WarningSearchCriteria true "criteria to find warnings"
func (s *service) setupFindWarningsDeepLink(app *fiber.App) {
	app.Post(
		s.BuildPath("/warnings/:identity/find"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
			wapimod.CustomerAdminUserRole,
			wapimod.UserUserRole,
		}),
		s.GetStrictAuthorizationHandler(s.criteriaLocationsExtractor(func() mod.LocationSearchCriteria {
			return modwarn.NewDefaultWarningSearchCriteria()
		})),
		s.findWarningsDeepLink,
	)
}

func (s *service) findWarningsDeepLink(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("findWarningsDeepLink")
	defer clog.OnExit("findWarningsDeepLink", err)

	i := ctx.Params("identity")
	identity, err := uuid.Parse(i)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred parsing the identity: %s", i), err))
	}

	var criteria = modwarn.NewDefaultWarningSearchCriteria()
	err = ctx.BodyParser(&criteria)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid criteria", err))
	}

	if userLocations := ctx.Locals(authsvc.UserLocationsLookup); userLocations != nil && criteria.Locations == nil {
		criteria.Locations = userLocations.([]uuid.UUID)
	}

	result, err := s.local.FindWarnings(*criteria, lo.ToPtr(identity))
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred finding warnings: %v", criteria), err))
	} else if result == nil {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to find warnings with: %v", criteria), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

// DeleteWarning godoc
// @Security ApiKeyAuth
// @Summary Delete a warning
// @Description Delete a warning from id
// @Tags warnings
// @Produce json
// @Success 200 {object} bool
// @Failure 400,404 {object} wapimod.ApiResult
// @Failure 500 {object} wapimod.ApiResult
// @Router /warnings/{identity} [delete]
// @Param identity path string true "Identity of the warning to delete"
func (s *service) setupDeleteWarning(app *fiber.App) {
	app.Delete(
		s.BuildPath("/warnings/:identity"),
		s.GetAuthorizationHandler([]authmod.IAuthUserRole{
			wapimod.DeveloperUserRole,
			wapimod.ThreatAnalystUserRole,
		}),
		s.deleteWarning,
	)
}

func (s *service) deleteWarning(ctx *fiber.Ctx) (err error) {
	clog.OnEntry("deleteWarning")
	defer clog.OnExit("deleteWarning", err)

	_identity := ctx.Params("identity")
	if _identity == "" {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("no identity specified", err))
	}
	identity, err := uuid.Parse(_identity)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred parsing the identity: %s", _identity), err))
	}

	result, err := s.local.DeleteWarning(identity)
	if err != nil {
		return ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError(fmt.Sprintf("an error occurred deleting warning: %v", identity), err))
	} else if !result {
		return ctx.Status(fiber.StatusNotFound).JSON(wapimod.NewApiError(fmt.Sprintf("failed to delete warning: %v", identity), err))
	} else {
		return ctx.Status(fiber.StatusOK).Status(fiber.StatusOK).JSON(result)
	}
}

func (s *service) criteriaLocationsExtractor(criteriaConstructor func() mod.LocationSearchCriteria) func(ctx *fiber.Ctx) ([]uuid.UUID, error) {
	return func(ctx *fiber.Ctx) ([]uuid.UUID, error) {
		criteria := criteriaConstructor()
		err := ctx.BodyParser(criteria)
		if err != nil {
			return nil, ctx.Status(fiber.StatusBadRequest).JSON(wapimod.NewApiError("invalid criteria specified", err))
		}

		return criteria.GetLocations(), nil
	}
}
