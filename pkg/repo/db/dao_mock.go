// Code generated by mockery. DO NOT EDIT.

//
//go:build !compile

package db

import (
	cjson "github.com/CyberOwlTeam/go-utilities/pkg/cutils/cjson"
	gorm "gorm.io/gorm"

	mock "github.com/stretchr/testify/mock"

	modinv "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"

	modnotif "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"

	modusr "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"

	modwarn "github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"

	sql "database/sql"

	uuid "github.com/google/uuid"
)

// MockDao is an autogenerated mock type for the Dao type
type MockDao struct {
	mock.Mock
}

type MockDao_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDao) EXPECT() *MockDao_Expecter {
	return &MockDao_Expecter{mock: &_m.Mock}
}

// CountTotalNumberOfWarnings provides a mock function with given fields: criteria, tx
func (_m *MockDao) CountTotalNumberOfWarnings(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB) (int, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, criteria)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CountTotalNumberOfWarnings")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, ...*gorm.DB) (int, error)); ok {
		return rf(criteria, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, ...*gorm.DB) int); ok {
		r0 = rf(criteria, tx...)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningSearchCriteria, ...*gorm.DB) error); ok {
		r1 = rf(criteria, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CountTotalNumberOfWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CountTotalNumberOfWarnings'
type MockDao_CountTotalNumberOfWarnings_Call struct {
	*mock.Call
}

// CountTotalNumberOfWarnings is a helper method to define mock.On call
//   - criteria modwarn.WarningSearchCriteria
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CountTotalNumberOfWarnings(criteria interface{}, tx ...interface{}) *MockDao_CountTotalNumberOfWarnings_Call {
	return &MockDao_CountTotalNumberOfWarnings_Call{Call: _e.mock.On("CountTotalNumberOfWarnings",
		append([]interface{}{criteria}, tx...)...)}
}

func (_c *MockDao_CountTotalNumberOfWarnings_Call) Run(run func(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB)) *MockDao_CountTotalNumberOfWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.WarningSearchCriteria), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CountTotalNumberOfWarnings_Call) Return(_a0 int, _a1 error) *MockDao_CountTotalNumberOfWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CountTotalNumberOfWarnings_Call) RunAndReturn(run func(modwarn.WarningSearchCriteria, ...*gorm.DB) (int, error)) *MockDao_CountTotalNumberOfWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAffectedItem provides a mock function with given fields: item, tx
func (_m *MockDao) CreateAffectedItem(item modnotif.AffectedItem, tx ...*gorm.DB) (*modnotif.AffectedItem, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, item)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem, ...*gorm.DB) (*modnotif.AffectedItem, error)); ok {
		return rf(item, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem, ...*gorm.DB) *modnotif.AffectedItem); ok {
		r0 = rf(item, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.AffectedItem, ...*gorm.DB) error); ok {
		r1 = rf(item, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAffectedItem'
type MockDao_CreateAffectedItem_Call struct {
	*mock.Call
}

// CreateAffectedItem is a helper method to define mock.On call
//   - item modnotif.AffectedItem
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateAffectedItem(item interface{}, tx ...interface{}) *MockDao_CreateAffectedItem_Call {
	return &MockDao_CreateAffectedItem_Call{Call: _e.mock.On("CreateAffectedItem",
		append([]interface{}{item}, tx...)...)}
}

func (_c *MockDao_CreateAffectedItem_Call) Run(run func(item modnotif.AffectedItem, tx ...*gorm.DB)) *MockDao_CreateAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.AffectedItem), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockDao_CreateAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateAffectedItem_Call) RunAndReturn(run func(modnotif.AffectedItem, ...*gorm.DB) (*modnotif.AffectedItem, error)) *MockDao_CreateAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// CreateAsset provides a mock function with given fields: asset, tx
func (_m *MockDao) CreateAsset(asset modinv.Asset, tx ...*gorm.DB) (*modinv.Asset, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, asset)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Asset, ...*gorm.DB) (*modinv.Asset, error)); ok {
		return rf(asset, tx...)
	}
	if rf, ok := ret.Get(0).(func(modinv.Asset, ...*gorm.DB) *modinv.Asset); ok {
		r0 = rf(asset, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Asset, ...*gorm.DB) error); ok {
		r1 = rf(asset, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateAsset'
type MockDao_CreateAsset_Call struct {
	*mock.Call
}

// CreateAsset is a helper method to define mock.On call
//   - asset modinv.Asset
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateAsset(asset interface{}, tx ...interface{}) *MockDao_CreateAsset_Call {
	return &MockDao_CreateAsset_Call{Call: _e.mock.On("CreateAsset",
		append([]interface{}{asset}, tx...)...)}
}

func (_c *MockDao_CreateAsset_Call) Run(run func(asset modinv.Asset, tx ...*gorm.DB)) *MockDao_CreateAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modinv.Asset), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockDao_CreateAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateAsset_Call) RunAndReturn(run func(modinv.Asset, ...*gorm.DB) (*modinv.Asset, error)) *MockDao_CreateAsset_Call {
	_c.Call.Return(run)
	return _c
}

// CreateLocation provides a mock function with given fields: location, tx
func (_m *MockDao) CreateLocation(location modinv.Location, tx ...*gorm.DB) (*modinv.Location, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, location)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Location, ...*gorm.DB) (*modinv.Location, error)); ok {
		return rf(location, tx...)
	}
	if rf, ok := ret.Get(0).(func(modinv.Location, ...*gorm.DB) *modinv.Location); ok {
		r0 = rf(location, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Location, ...*gorm.DB) error); ok {
		r1 = rf(location, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateLocation'
type MockDao_CreateLocation_Call struct {
	*mock.Call
}

// CreateLocation is a helper method to define mock.On call
//   - location modinv.Location
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateLocation(location interface{}, tx ...interface{}) *MockDao_CreateLocation_Call {
	return &MockDao_CreateLocation_Call{Call: _e.mock.On("CreateLocation",
		append([]interface{}{location}, tx...)...)}
}

func (_c *MockDao_CreateLocation_Call) Run(run func(location modinv.Location, tx ...*gorm.DB)) *MockDao_CreateLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modinv.Location), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockDao_CreateLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateLocation_Call) RunAndReturn(run func(modinv.Location, ...*gorm.DB) (*modinv.Location, error)) *MockDao_CreateLocation_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationQueue provides a mock function with given fields: notificationQueue, tx
func (_m *MockDao) CreateNotificationQueue(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB) (*modnotif.NotificationQueue, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, notificationQueue)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationQueue")
	}

	var r0 *modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue, ...*gorm.DB) (*modnotif.NotificationQueue, error)); ok {
		return rf(notificationQueue, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue, ...*gorm.DB) *modnotif.NotificationQueue); ok {
		r0 = rf(notificationQueue, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueue, ...*gorm.DB) error); ok {
		r1 = rf(notificationQueue, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationQueue'
type MockDao_CreateNotificationQueue_Call struct {
	*mock.Call
}

// CreateNotificationQueue is a helper method to define mock.On call
//   - notificationQueue modnotif.NotificationQueue
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateNotificationQueue(notificationQueue interface{}, tx ...interface{}) *MockDao_CreateNotificationQueue_Call {
	return &MockDao_CreateNotificationQueue_Call{Call: _e.mock.On("CreateNotificationQueue",
		append([]interface{}{notificationQueue}, tx...)...)}
}

func (_c *MockDao_CreateNotificationQueue_Call) Run(run func(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB)) *MockDao_CreateNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationQueue), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateNotificationQueue_Call) Return(_a0 *modnotif.NotificationQueue, _a1 error) *MockDao_CreateNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueue, ...*gorm.DB) (*modnotif.NotificationQueue, error)) *MockDao_CreateNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationRule provides a mock function with given fields: rule, tx
func (_m *MockDao) CreateNotificationRule(rule modnotif.NotificationRule, tx ...*gorm.DB) (*modnotif.NotificationRule, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, rule)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule, ...*gorm.DB) (*modnotif.NotificationRule, error)); ok {
		return rf(rule, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule, ...*gorm.DB) *modnotif.NotificationRule); ok {
		r0 = rf(rule, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationRule, ...*gorm.DB) error); ok {
		r1 = rf(rule, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationRule'
type MockDao_CreateNotificationRule_Call struct {
	*mock.Call
}

// CreateNotificationRule is a helper method to define mock.On call
//   - rule modnotif.NotificationRule
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateNotificationRule(rule interface{}, tx ...interface{}) *MockDao_CreateNotificationRule_Call {
	return &MockDao_CreateNotificationRule_Call{Call: _e.mock.On("CreateNotificationRule",
		append([]interface{}{rule}, tx...)...)}
}

func (_c *MockDao_CreateNotificationRule_Call) Run(run func(rule modnotif.NotificationRule, tx ...*gorm.DB)) *MockDao_CreateNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationRule), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockDao_CreateNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateNotificationRule_Call) RunAndReturn(run func(modnotif.NotificationRule, ...*gorm.DB) (*modnotif.NotificationRule, error)) *MockDao_CreateNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// CreateNotificationSetting provides a mock function with given fields: setting, tx
func (_m *MockDao) CreateNotificationSetting(setting modnotif.NotificationSetting, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, setting)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting, ...*gorm.DB) (*modnotif.NotificationSetting, error)); ok {
		return rf(setting, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting, ...*gorm.DB) *modnotif.NotificationSetting); ok {
		r0 = rf(setting, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationSetting, ...*gorm.DB) error); ok {
		r1 = rf(setting, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateNotificationSetting'
type MockDao_CreateNotificationSetting_Call struct {
	*mock.Call
}

// CreateNotificationSetting is a helper method to define mock.On call
//   - setting modnotif.NotificationSetting
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateNotificationSetting(setting interface{}, tx ...interface{}) *MockDao_CreateNotificationSetting_Call {
	return &MockDao_CreateNotificationSetting_Call{Call: _e.mock.On("CreateNotificationSetting",
		append([]interface{}{setting}, tx...)...)}
}

func (_c *MockDao_CreateNotificationSetting_Call) Run(run func(setting modnotif.NotificationSetting, tx ...*gorm.DB)) *MockDao_CreateNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationSetting), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockDao_CreateNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateNotificationSetting_Call) RunAndReturn(run func(modnotif.NotificationSetting, ...*gorm.DB) (*modnotif.NotificationSetting, error)) *MockDao_CreateNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// CreateUser provides a mock function with given fields: user, tx
func (_m *MockDao) CreateUser(user modusr.User, tx ...*gorm.DB) (*modusr.User, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, user)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(modusr.User, ...*gorm.DB) (*modusr.User, error)); ok {
		return rf(user, tx...)
	}
	if rf, ok := ret.Get(0).(func(modusr.User, ...*gorm.DB) *modusr.User); ok {
		r0 = rf(user, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(modusr.User, ...*gorm.DB) error); ok {
		r1 = rf(user, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateUser'
type MockDao_CreateUser_Call struct {
	*mock.Call
}

// CreateUser is a helper method to define mock.On call
//   - user modusr.User
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateUser(user interface{}, tx ...interface{}) *MockDao_CreateUser_Call {
	return &MockDao_CreateUser_Call{Call: _e.mock.On("CreateUser",
		append([]interface{}{user}, tx...)...)}
}

func (_c *MockDao_CreateUser_Call) Run(run func(user modusr.User, tx ...*gorm.DB)) *MockDao_CreateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modusr.User), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateUser_Call) Return(_a0 *modusr.User, _a1 error) *MockDao_CreateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateUser_Call) RunAndReturn(run func(modusr.User, ...*gorm.DB) (*modusr.User, error)) *MockDao_CreateUser_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWarning provides a mock function with given fields: warning, tx
func (_m *MockDao) CreateWarning(warning modwarn.Warning, tx ...*gorm.DB) (*modwarn.Warning, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warning)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.Warning, ...*gorm.DB) (*modwarn.Warning, error)); ok {
		return rf(warning, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.Warning, ...*gorm.DB) *modwarn.Warning); ok {
		r0 = rf(warning, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.Warning, ...*gorm.DB) error); ok {
		r1 = rf(warning, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWarning'
type MockDao_CreateWarning_Call struct {
	*mock.Call
}

// CreateWarning is a helper method to define mock.On call
//   - warning modwarn.Warning
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateWarning(warning interface{}, tx ...interface{}) *MockDao_CreateWarning_Call {
	return &MockDao_CreateWarning_Call{Call: _e.mock.On("CreateWarning",
		append([]interface{}{warning}, tx...)...)}
}

func (_c *MockDao_CreateWarning_Call) Run(run func(warning modwarn.Warning, tx ...*gorm.DB)) *MockDao_CreateWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.Warning), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockDao_CreateWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateWarning_Call) RunAndReturn(run func(modwarn.Warning, ...*gorm.DB) (*modwarn.Warning, error)) *MockDao_CreateWarning_Call {
	_c.Call.Return(run)
	return _c
}

// CreateWarningHistory provides a mock function with given fields: warningHistory, tx
func (_m *MockDao) CreateWarningHistory(warningHistory modwarn.WarningHistory, tx ...*gorm.DB) (*modwarn.WarningHistory, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warningHistory)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for CreateWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory, ...*gorm.DB) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory, ...*gorm.DB) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory, ...*gorm.DB) error); ok {
		r1 = rf(warningHistory, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_CreateWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CreateWarningHistory'
type MockDao_CreateWarningHistory_Call struct {
	*mock.Call
}

// CreateWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) CreateWarningHistory(warningHistory interface{}, tx ...interface{}) *MockDao_CreateWarningHistory_Call {
	return &MockDao_CreateWarningHistory_Call{Call: _e.mock.On("CreateWarningHistory",
		append([]interface{}{warningHistory}, tx...)...)}
}

func (_c *MockDao_CreateWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory, tx ...*gorm.DB)) *MockDao_CreateWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.WarningHistory), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_CreateWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockDao_CreateWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_CreateWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory, ...*gorm.DB) (*modwarn.WarningHistory, error)) *MockDao_CreateWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAffectedItem provides a mock function with given fields: identity, tx
func (_m *MockDao) DeleteAffectedItem(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAffectedItem")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(identity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAffectedItem'
type MockDao_DeleteAffectedItem_Call struct {
	*mock.Call
}

// DeleteAffectedItem is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteAffectedItem(identity interface{}, tx ...interface{}) *MockDao_DeleteAffectedItem_Call {
	return &MockDao_DeleteAffectedItem_Call{Call: _e.mock.On("DeleteAffectedItem",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_DeleteAffectedItem_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteAffectedItem_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteAffectedItem_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAffectedItem4NotificationRule provides a mock function with given fields: notifRuleIdentity, tx
func (_m *MockDao) DeleteAffectedItem4NotificationRule(notifRuleIdentity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, notifRuleIdentity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAffectedItem4NotificationRule")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(notifRuleIdentity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(notifRuleIdentity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(notifRuleIdentity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteAffectedItem4NotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAffectedItem4NotificationRule'
type MockDao_DeleteAffectedItem4NotificationRule_Call struct {
	*mock.Call
}

// DeleteAffectedItem4NotificationRule is a helper method to define mock.On call
//   - notifRuleIdentity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteAffectedItem4NotificationRule(notifRuleIdentity interface{}, tx ...interface{}) *MockDao_DeleteAffectedItem4NotificationRule_Call {
	return &MockDao_DeleteAffectedItem4NotificationRule_Call{Call: _e.mock.On("DeleteAffectedItem4NotificationRule",
		append([]interface{}{notifRuleIdentity}, tx...)...)}
}

func (_c *MockDao_DeleteAffectedItem4NotificationRule_Call) Run(run func(notifRuleIdentity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteAffectedItem4NotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteAffectedItem4NotificationRule_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteAffectedItem4NotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteAffectedItem4NotificationRule_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteAffectedItem4NotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAsset provides a mock function with given fields: id, tx
func (_m *MockDao) DeleteAsset(id uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAsset")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(id, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(id, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(id, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAsset'
type MockDao_DeleteAsset_Call struct {
	*mock.Call
}

// DeleteAsset is a helper method to define mock.On call
//   - id uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteAsset(id interface{}, tx ...interface{}) *MockDao_DeleteAsset_Call {
	return &MockDao_DeleteAsset_Call{Call: _e.mock.On("DeleteAsset",
		append([]interface{}{id}, tx...)...)}
}

func (_c *MockDao_DeleteAsset_Call) Run(run func(id uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteAsset_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteAsset_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteAsset_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteAssetsForLocation provides a mock function with given fields: locationId, tx
func (_m *MockDao) DeleteAssetsForLocation(locationId uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, locationId)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteAssetsForLocation")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(locationId, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(locationId, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(locationId, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteAssetsForLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteAssetsForLocation'
type MockDao_DeleteAssetsForLocation_Call struct {
	*mock.Call
}

// DeleteAssetsForLocation is a helper method to define mock.On call
//   - locationId uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteAssetsForLocation(locationId interface{}, tx ...interface{}) *MockDao_DeleteAssetsForLocation_Call {
	return &MockDao_DeleteAssetsForLocation_Call{Call: _e.mock.On("DeleteAssetsForLocation",
		append([]interface{}{locationId}, tx...)...)}
}

func (_c *MockDao_DeleteAssetsForLocation_Call) Run(run func(locationId uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteAssetsForLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteAssetsForLocation_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteAssetsForLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteAssetsForLocation_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteAssetsForLocation_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteLocation provides a mock function with given fields: identity, tx
func (_m *MockDao) DeleteLocation(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteLocation")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(identity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteLocation'
type MockDao_DeleteLocation_Call struct {
	*mock.Call
}

// DeleteLocation is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteLocation(identity interface{}, tx ...interface{}) *MockDao_DeleteLocation_Call {
	return &MockDao_DeleteLocation_Call{Call: _e.mock.On("DeleteLocation",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_DeleteLocation_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteLocation_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteLocation_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteLocation_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationQueue provides a mock function with given fields: id, tx
func (_m *MockDao) DeleteNotificationQueue(id modnotif.NotificationQueueIdentity, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationQueue")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity, ...*gorm.DB) (bool, error)); ok {
		return rf(id, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity, ...*gorm.DB) bool); ok {
		r0 = rf(id, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueueIdentity, ...*gorm.DB) error); ok {
		r1 = rf(id, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationQueue'
type MockDao_DeleteNotificationQueue_Call struct {
	*mock.Call
}

// DeleteNotificationQueue is a helper method to define mock.On call
//   - id modnotif.NotificationQueueIdentity
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteNotificationQueue(id interface{}, tx ...interface{}) *MockDao_DeleteNotificationQueue_Call {
	return &MockDao_DeleteNotificationQueue_Call{Call: _e.mock.On("DeleteNotificationQueue",
		append([]interface{}{id}, tx...)...)}
}

func (_c *MockDao_DeleteNotificationQueue_Call) Run(run func(id modnotif.NotificationQueueIdentity, tx ...*gorm.DB)) *MockDao_DeleteNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationQueueIdentity), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteNotificationQueue_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueueIdentity, ...*gorm.DB) (bool, error)) *MockDao_DeleteNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationRule provides a mock function with given fields: identity, tx
func (_m *MockDao) DeleteNotificationRule(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationRule")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(identity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationRule'
type MockDao_DeleteNotificationRule_Call struct {
	*mock.Call
}

// DeleteNotificationRule is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteNotificationRule(identity interface{}, tx ...interface{}) *MockDao_DeleteNotificationRule_Call {
	return &MockDao_DeleteNotificationRule_Call{Call: _e.mock.On("DeleteNotificationRule",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_DeleteNotificationRule_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteNotificationRule_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteNotificationRule_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationRule4NotificationSetting provides a mock function with given fields: notifSettingIdentity, tx
func (_m *MockDao) DeleteNotificationRule4NotificationSetting(notifSettingIdentity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, notifSettingIdentity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationRule4NotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(notifSettingIdentity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(notifSettingIdentity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(notifSettingIdentity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteNotificationRule4NotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationRule4NotificationSetting'
type MockDao_DeleteNotificationRule4NotificationSetting_Call struct {
	*mock.Call
}

// DeleteNotificationRule4NotificationSetting is a helper method to define mock.On call
//   - notifSettingIdentity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteNotificationRule4NotificationSetting(notifSettingIdentity interface{}, tx ...interface{}) *MockDao_DeleteNotificationRule4NotificationSetting_Call {
	return &MockDao_DeleteNotificationRule4NotificationSetting_Call{Call: _e.mock.On("DeleteNotificationRule4NotificationSetting",
		append([]interface{}{notifSettingIdentity}, tx...)...)}
}

func (_c *MockDao_DeleteNotificationRule4NotificationSetting_Call) Run(run func(notifSettingIdentity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteNotificationRule4NotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteNotificationRule4NotificationSetting_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteNotificationRule4NotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteNotificationRule4NotificationSetting_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteNotificationRule4NotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationSetting provides a mock function with given fields: identity, tx
func (_m *MockDao) DeleteNotificationSetting(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(identity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationSetting'
type MockDao_DeleteNotificationSetting_Call struct {
	*mock.Call
}

// DeleteNotificationSetting is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteNotificationSetting(identity interface{}, tx ...interface{}) *MockDao_DeleteNotificationSetting_Call {
	return &MockDao_DeleteNotificationSetting_Call{Call: _e.mock.On("DeleteNotificationSetting",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_DeleteNotificationSetting_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteNotificationSetting_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteNotificationSetting_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteNotificationSettingsUserForNotificationSetting provides a mock function with given fields: notifSettingIdentity, tx
func (_m *MockDao) DeleteNotificationSettingsUserForNotificationSetting(notifSettingIdentity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, notifSettingIdentity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotificationSettingsUserForNotificationSetting")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(notifSettingIdentity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(notifSettingIdentity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(notifSettingIdentity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteNotificationSettingsUserForNotificationSetting'
type MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call struct {
	*mock.Call
}

// DeleteNotificationSettingsUserForNotificationSetting is a helper method to define mock.On call
//   - notifSettingIdentity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteNotificationSettingsUserForNotificationSetting(notifSettingIdentity interface{}, tx ...interface{}) *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call {
	return &MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call{Call: _e.mock.On("DeleteNotificationSettingsUserForNotificationSetting",
		append([]interface{}{notifSettingIdentity}, tx...)...)}
}

func (_c *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call) Run(run func(notifSettingIdentity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteNotificationSettingsUserForNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUser provides a mock function with given fields: id, tx
func (_m *MockDao) DeleteUser(id uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteUser")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(id, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(id, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(id, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUser'
type MockDao_DeleteUser_Call struct {
	*mock.Call
}

// DeleteUser is a helper method to define mock.On call
//   - id uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteUser(id interface{}, tx ...interface{}) *MockDao_DeleteUser_Call {
	return &MockDao_DeleteUser_Call{Call: _e.mock.On("DeleteUser",
		append([]interface{}{id}, tx...)...)}
}

func (_c *MockDao_DeleteUser_Call) Run(run func(id uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteUser_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteUser_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteUser_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarning provides a mock function with given fields: identity, tx
func (_m *MockDao) DeleteWarning(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarning")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(identity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarning'
type MockDao_DeleteWarning_Call struct {
	*mock.Call
}

// DeleteWarning is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteWarning(identity interface{}, tx ...interface{}) *MockDao_DeleteWarning_Call {
	return &MockDao_DeleteWarning_Call{Call: _e.mock.On("DeleteWarning",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_DeleteWarning_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteWarning_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteWarning_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteWarning_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarningHistory provides a mock function with given fields: identity, tx
func (_m *MockDao) DeleteWarningHistory(identity uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarningHistory")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(identity, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarningHistory'
type MockDao_DeleteWarningHistory_Call struct {
	*mock.Call
}

// DeleteWarningHistory is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteWarningHistory(identity interface{}, tx ...interface{}) *MockDao_DeleteWarningHistory_Call {
	return &MockDao_DeleteWarningHistory_Call{Call: _e.mock.On("DeleteWarningHistory",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_DeleteWarningHistory_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteWarningHistory_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteWarningHistory_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteWarningsHistoryForWarningId provides a mock function with given fields: warningId, tx
func (_m *MockDao) DeleteWarningsHistoryForWarningId(warningId uuid.UUID, tx ...*gorm.DB) (bool, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warningId)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for DeleteWarningsHistoryForWarningId")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (bool, error)); ok {
		return rf(warningId, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) bool); ok {
		r0 = rf(warningId, tx...)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(warningId, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_DeleteWarningsHistoryForWarningId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteWarningsHistoryForWarningId'
type MockDao_DeleteWarningsHistoryForWarningId_Call struct {
	*mock.Call
}

// DeleteWarningsHistoryForWarningId is a helper method to define mock.On call
//   - warningId uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) DeleteWarningsHistoryForWarningId(warningId interface{}, tx ...interface{}) *MockDao_DeleteWarningsHistoryForWarningId_Call {
	return &MockDao_DeleteWarningsHistoryForWarningId_Call{Call: _e.mock.On("DeleteWarningsHistoryForWarningId",
		append([]interface{}{warningId}, tx...)...)}
}

func (_c *MockDao_DeleteWarningsHistoryForWarningId_Call) Run(run func(warningId uuid.UUID, tx ...*gorm.DB)) *MockDao_DeleteWarningsHistoryForWarningId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_DeleteWarningsHistoryForWarningId_Call) Return(_a0 bool, _a1 error) *MockDao_DeleteWarningsHistoryForWarningId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_DeleteWarningsHistoryForWarningId_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (bool, error)) *MockDao_DeleteWarningsHistoryForWarningId_Call {
	_c.Call.Return(run)
	return _c
}

// FindWarnings provides a mock function with given fields: criteria, tx
func (_m *MockDao) FindWarnings(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB) ([]modwarn.Warning, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, criteria)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindWarnings")
	}

	var r0 []modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, ...*gorm.DB) ([]modwarn.Warning, error)); ok {
		return rf(criteria, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, ...*gorm.DB) []modwarn.Warning); ok {
		r0 = rf(criteria, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningSearchCriteria, ...*gorm.DB) error); ok {
		r1 = rf(criteria, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_FindWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindWarnings'
type MockDao_FindWarnings_Call struct {
	*mock.Call
}

// FindWarnings is a helper method to define mock.On call
//   - criteria modwarn.WarningSearchCriteria
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) FindWarnings(criteria interface{}, tx ...interface{}) *MockDao_FindWarnings_Call {
	return &MockDao_FindWarnings_Call{Call: _e.mock.On("FindWarnings",
		append([]interface{}{criteria}, tx...)...)}
}

func (_c *MockDao_FindWarnings_Call) Run(run func(criteria modwarn.WarningSearchCriteria, tx ...*gorm.DB)) *MockDao_FindWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.WarningSearchCriteria), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_FindWarnings_Call) Return(_a0 []modwarn.Warning, _a1 error) *MockDao_FindWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_FindWarnings_Call) RunAndReturn(run func(modwarn.WarningSearchCriteria, ...*gorm.DB) ([]modwarn.Warning, error)) *MockDao_FindWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// FindWarningsOffset provides a mock function with given fields: criteria, identity, tx
func (_m *MockDao) FindWarningsOffset(criteria modwarn.WarningSearchCriteria, identity uuid.UUID, tx ...*gorm.DB) (int, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, criteria, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FindWarningsOffset")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, uuid.UUID, ...*gorm.DB) (int, error)); ok {
		return rf(criteria, identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningSearchCriteria, uuid.UUID, ...*gorm.DB) int); ok {
		r0 = rf(criteria, identity, tx...)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningSearchCriteria, uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(criteria, identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_FindWarningsOffset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FindWarningsOffset'
type MockDao_FindWarningsOffset_Call struct {
	*mock.Call
}

// FindWarningsOffset is a helper method to define mock.On call
//   - criteria modwarn.WarningSearchCriteria
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) FindWarningsOffset(criteria interface{}, identity interface{}, tx ...interface{}) *MockDao_FindWarningsOffset_Call {
	return &MockDao_FindWarningsOffset_Call{Call: _e.mock.On("FindWarningsOffset",
		append([]interface{}{criteria, identity}, tx...)...)}
}

func (_c *MockDao_FindWarningsOffset_Call) Run(run func(criteria modwarn.WarningSearchCriteria, identity uuid.UUID, tx ...*gorm.DB)) *MockDao_FindWarningsOffset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.WarningSearchCriteria), args[1].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_FindWarningsOffset_Call) Return(_a0 int, _a1 error) *MockDao_FindWarningsOffset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_FindWarningsOffset_Call) RunAndReturn(run func(modwarn.WarningSearchCriteria, uuid.UUID, ...*gorm.DB) (int, error)) *MockDao_FindWarningsOffset_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAffectedItems provides a mock function with given fields: tx
func (_m *MockDao) GetAllAffectedItems(tx ...*gorm.DB) ([]modnotif.AffectedItem, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAffectedItems")
	}

	var r0 []modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modnotif.AffectedItem, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modnotif.AffectedItem); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllAffectedItems_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAffectedItems'
type MockDao_GetAllAffectedItems_Call struct {
	*mock.Call
}

// GetAllAffectedItems is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllAffectedItems(tx ...interface{}) *MockDao_GetAllAffectedItems_Call {
	return &MockDao_GetAllAffectedItems_Call{Call: _e.mock.On("GetAllAffectedItems",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllAffectedItems_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllAffectedItems_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllAffectedItems_Call) Return(_a0 []modnotif.AffectedItem, _a1 error) *MockDao_GetAllAffectedItems_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllAffectedItems_Call) RunAndReturn(run func(...*gorm.DB) ([]modnotif.AffectedItem, error)) *MockDao_GetAllAffectedItems_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllAssets provides a mock function with given fields: tx
func (_m *MockDao) GetAllAssets(tx ...*gorm.DB) ([]modinv.Asset, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllAssets")
	}

	var r0 []modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modinv.Asset, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modinv.Asset); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllAssets_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllAssets'
type MockDao_GetAllAssets_Call struct {
	*mock.Call
}

// GetAllAssets is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllAssets(tx ...interface{}) *MockDao_GetAllAssets_Call {
	return &MockDao_GetAllAssets_Call{Call: _e.mock.On("GetAllAssets",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllAssets_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllAssets_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllAssets_Call) Return(_a0 []modinv.Asset, _a1 error) *MockDao_GetAllAssets_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllAssets_Call) RunAndReturn(run func(...*gorm.DB) ([]modinv.Asset, error)) *MockDao_GetAllAssets_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllLocations provides a mock function with given fields: tx
func (_m *MockDao) GetAllLocations(tx ...*gorm.DB) ([]modinv.Location, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllLocations")
	}

	var r0 []modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modinv.Location, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modinv.Location); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllLocations_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllLocations'
type MockDao_GetAllLocations_Call struct {
	*mock.Call
}

// GetAllLocations is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllLocations(tx ...interface{}) *MockDao_GetAllLocations_Call {
	return &MockDao_GetAllLocations_Call{Call: _e.mock.On("GetAllLocations",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllLocations_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllLocations_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllLocations_Call) Return(_a0 []modinv.Location, _a1 error) *MockDao_GetAllLocations_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllLocations_Call) RunAndReturn(run func(...*gorm.DB) ([]modinv.Location, error)) *MockDao_GetAllLocations_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationQueues provides a mock function with given fields: tx
func (_m *MockDao) GetAllNotificationQueues(tx ...*gorm.DB) ([]modnotif.NotificationQueue, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationQueues")
	}

	var r0 []modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modnotif.NotificationQueue, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modnotif.NotificationQueue); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllNotificationQueues_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationQueues'
type MockDao_GetAllNotificationQueues_Call struct {
	*mock.Call
}

// GetAllNotificationQueues is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllNotificationQueues(tx ...interface{}) *MockDao_GetAllNotificationQueues_Call {
	return &MockDao_GetAllNotificationQueues_Call{Call: _e.mock.On("GetAllNotificationQueues",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllNotificationQueues_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllNotificationQueues_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllNotificationQueues_Call) Return(_a0 []modnotif.NotificationQueue, _a1 error) *MockDao_GetAllNotificationQueues_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllNotificationQueues_Call) RunAndReturn(run func(...*gorm.DB) ([]modnotif.NotificationQueue, error)) *MockDao_GetAllNotificationQueues_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationQueuesForUserAndFrequency provides a mock function with given fields: user, frequency, tx
func (_m *MockDao) GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency, tx ...*gorm.DB) ([]modnotif.NotificationQueue, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, user, frequency)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationQueuesForUserAndFrequency")
	}

	var r0 []modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, modnotif.NotificationFrequency, ...*gorm.DB) ([]modnotif.NotificationQueue, error)); ok {
		return rf(user, frequency, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, modnotif.NotificationFrequency, ...*gorm.DB) []modnotif.NotificationQueue); ok {
		r0 = rf(user, frequency, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, modnotif.NotificationFrequency, ...*gorm.DB) error); ok {
		r1 = rf(user, frequency, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllNotificationQueuesForUserAndFrequency_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationQueuesForUserAndFrequency'
type MockDao_GetAllNotificationQueuesForUserAndFrequency_Call struct {
	*mock.Call
}

// GetAllNotificationQueuesForUserAndFrequency is a helper method to define mock.On call
//   - user uuid.UUID
//   - frequency modnotif.NotificationFrequency
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllNotificationQueuesForUserAndFrequency(user interface{}, frequency interface{}, tx ...interface{}) *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call {
	return &MockDao_GetAllNotificationQueuesForUserAndFrequency_Call{Call: _e.mock.On("GetAllNotificationQueuesForUserAndFrequency",
		append([]interface{}{user, frequency}, tx...)...)}
}

func (_c *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call) Run(run func(user uuid.UUID, frequency modnotif.NotificationFrequency, tx ...*gorm.DB)) *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), args[1].(modnotif.NotificationFrequency), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call) Return(_a0 []modnotif.NotificationQueue, _a1 error) *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call) RunAndReturn(run func(uuid.UUID, modnotif.NotificationFrequency, ...*gorm.DB) ([]modnotif.NotificationQueue, error)) *MockDao_GetAllNotificationQueuesForUserAndFrequency_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationRules provides a mock function with given fields: tx
func (_m *MockDao) GetAllNotificationRules(tx ...*gorm.DB) ([]modnotif.NotificationRule, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationRules")
	}

	var r0 []modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modnotif.NotificationRule, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modnotif.NotificationRule); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllNotificationRules_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationRules'
type MockDao_GetAllNotificationRules_Call struct {
	*mock.Call
}

// GetAllNotificationRules is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllNotificationRules(tx ...interface{}) *MockDao_GetAllNotificationRules_Call {
	return &MockDao_GetAllNotificationRules_Call{Call: _e.mock.On("GetAllNotificationRules",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllNotificationRules_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllNotificationRules_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllNotificationRules_Call) Return(_a0 []modnotif.NotificationRule, _a1 error) *MockDao_GetAllNotificationRules_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllNotificationRules_Call) RunAndReturn(run func(...*gorm.DB) ([]modnotif.NotificationRule, error)) *MockDao_GetAllNotificationRules_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllNotificationSettings provides a mock function with given fields: tx
func (_m *MockDao) GetAllNotificationSettings(tx ...*gorm.DB) ([]modnotif.NotificationSetting, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllNotificationSettings")
	}

	var r0 []modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modnotif.NotificationSetting, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modnotif.NotificationSetting); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllNotificationSettings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllNotificationSettings'
type MockDao_GetAllNotificationSettings_Call struct {
	*mock.Call
}

// GetAllNotificationSettings is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllNotificationSettings(tx ...interface{}) *MockDao_GetAllNotificationSettings_Call {
	return &MockDao_GetAllNotificationSettings_Call{Call: _e.mock.On("GetAllNotificationSettings",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllNotificationSettings_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllNotificationSettings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllNotificationSettings_Call) Return(_a0 []modnotif.NotificationSetting, _a1 error) *MockDao_GetAllNotificationSettings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllNotificationSettings_Call) RunAndReturn(run func(...*gorm.DB) ([]modnotif.NotificationSetting, error)) *MockDao_GetAllNotificationSettings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllUsers provides a mock function with given fields: tx
func (_m *MockDao) GetAllUsers(tx ...*gorm.DB) ([]modusr.User, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllUsers")
	}

	var r0 []modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modusr.User, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modusr.User); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllUsers'
type MockDao_GetAllUsers_Call struct {
	*mock.Call
}

// GetAllUsers is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllUsers(tx ...interface{}) *MockDao_GetAllUsers_Call {
	return &MockDao_GetAllUsers_Call{Call: _e.mock.On("GetAllUsers",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllUsers_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllUsers_Call) Return(_a0 []modusr.User, _a1 error) *MockDao_GetAllUsers_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllUsers_Call) RunAndReturn(run func(...*gorm.DB) ([]modusr.User, error)) *MockDao_GetAllUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarnings provides a mock function with given fields: tx
func (_m *MockDao) GetAllWarnings(tx ...*gorm.DB) ([]modwarn.Warning, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarnings")
	}

	var r0 []modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modwarn.Warning, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modwarn.Warning); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllWarnings_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarnings'
type MockDao_GetAllWarnings_Call struct {
	*mock.Call
}

// GetAllWarnings is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllWarnings(tx ...interface{}) *MockDao_GetAllWarnings_Call {
	return &MockDao_GetAllWarnings_Call{Call: _e.mock.On("GetAllWarnings",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllWarnings_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllWarnings_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllWarnings_Call) Return(_a0 []modwarn.Warning, _a1 error) *MockDao_GetAllWarnings_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllWarnings_Call) RunAndReturn(run func(...*gorm.DB) ([]modwarn.Warning, error)) *MockDao_GetAllWarnings_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarningsHistory provides a mock function with given fields: tx
func (_m *MockDao) GetAllWarningsHistory(tx ...*gorm.DB) ([]modwarn.WarningHistory, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarningsHistory")
	}

	var r0 []modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) ([]modwarn.WarningHistory, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) []modwarn.WarningHistory); ok {
		r0 = rf(tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllWarningsHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarningsHistory'
type MockDao_GetAllWarningsHistory_Call struct {
	*mock.Call
}

// GetAllWarningsHistory is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllWarningsHistory(tx ...interface{}) *MockDao_GetAllWarningsHistory_Call {
	return &MockDao_GetAllWarningsHistory_Call{Call: _e.mock.On("GetAllWarningsHistory",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetAllWarningsHistory_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetAllWarningsHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllWarningsHistory_Call) Return(_a0 []modwarn.WarningHistory, _a1 error) *MockDao_GetAllWarningsHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllWarningsHistory_Call) RunAndReturn(run func(...*gorm.DB) ([]modwarn.WarningHistory, error)) *MockDao_GetAllWarningsHistory_Call {
	_c.Call.Return(run)
	return _c
}

// GetAllWarningsHistoryForWarningId provides a mock function with given fields: warningId, tx
func (_m *MockDao) GetAllWarningsHistoryForWarningId(warningId uuid.UUID, tx ...*gorm.DB) ([]modwarn.WarningHistory, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warningId)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetAllWarningsHistoryForWarningId")
	}

	var r0 []modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) ([]modwarn.WarningHistory, error)); ok {
		return rf(warningId, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) []modwarn.WarningHistory); ok {
		r0 = rf(warningId, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(warningId, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetAllWarningsHistoryForWarningId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetAllWarningsHistoryForWarningId'
type MockDao_GetAllWarningsHistoryForWarningId_Call struct {
	*mock.Call
}

// GetAllWarningsHistoryForWarningId is a helper method to define mock.On call
//   - warningId uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetAllWarningsHistoryForWarningId(warningId interface{}, tx ...interface{}) *MockDao_GetAllWarningsHistoryForWarningId_Call {
	return &MockDao_GetAllWarningsHistoryForWarningId_Call{Call: _e.mock.On("GetAllWarningsHistoryForWarningId",
		append([]interface{}{warningId}, tx...)...)}
}

func (_c *MockDao_GetAllWarningsHistoryForWarningId_Call) Run(run func(warningId uuid.UUID, tx ...*gorm.DB)) *MockDao_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetAllWarningsHistoryForWarningId_Call) Return(_a0 []modwarn.WarningHistory, _a1 error) *MockDao_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetAllWarningsHistoryForWarningId_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) ([]modwarn.WarningHistory, error)) *MockDao_GetAllWarningsHistoryForWarningId_Call {
	_c.Call.Return(run)
	return _c
}

// GetCurrentWarningNumberSequence provides a mock function with given fields: tx
func (_m *MockDao) GetCurrentWarningNumberSequence(tx ...*gorm.DB) (string, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetCurrentWarningNumberSequence")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) (string, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) string); ok {
		r0 = rf(tx...)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetCurrentWarningNumberSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCurrentWarningNumberSequence'
type MockDao_GetCurrentWarningNumberSequence_Call struct {
	*mock.Call
}

// GetCurrentWarningNumberSequence is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetCurrentWarningNumberSequence(tx ...interface{}) *MockDao_GetCurrentWarningNumberSequence_Call {
	return &MockDao_GetCurrentWarningNumberSequence_Call{Call: _e.mock.On("GetCurrentWarningNumberSequence",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetCurrentWarningNumberSequence_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetCurrentWarningNumberSequence_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetCurrentWarningNumberSequence_Call) Return(_a0 string, _a1 error) *MockDao_GetCurrentWarningNumberSequence_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetCurrentWarningNumberSequence_Call) RunAndReturn(run func(...*gorm.DB) (string, error)) *MockDao_GetCurrentWarningNumberSequence_Call {
	_c.Call.Return(run)
	return _c
}

// GetLatestWarningHistoryForWarningId provides a mock function with given fields: warningId, tx
func (_m *MockDao) GetLatestWarningHistoryForWarningId(warningId uuid.UUID, tx ...*gorm.DB) (*modwarn.WarningHistory, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warningId)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetLatestWarningHistoryForWarningId")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modwarn.WarningHistory, error)); ok {
		return rf(warningId, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modwarn.WarningHistory); ok {
		r0 = rf(warningId, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(warningId, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetLatestWarningHistoryForWarningId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetLatestWarningHistoryForWarningId'
type MockDao_GetLatestWarningHistoryForWarningId_Call struct {
	*mock.Call
}

// GetLatestWarningHistoryForWarningId is a helper method to define mock.On call
//   - warningId uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetLatestWarningHistoryForWarningId(warningId interface{}, tx ...interface{}) *MockDao_GetLatestWarningHistoryForWarningId_Call {
	return &MockDao_GetLatestWarningHistoryForWarningId_Call{Call: _e.mock.On("GetLatestWarningHistoryForWarningId",
		append([]interface{}{warningId}, tx...)...)}
}

func (_c *MockDao_GetLatestWarningHistoryForWarningId_Call) Run(run func(warningId uuid.UUID, tx ...*gorm.DB)) *MockDao_GetLatestWarningHistoryForWarningId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetLatestWarningHistoryForWarningId_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockDao_GetLatestWarningHistoryForWarningId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetLatestWarningHistoryForWarningId_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modwarn.WarningHistory, error)) *MockDao_GetLatestWarningHistoryForWarningId_Call {
	_c.Call.Return(run)
	return _c
}

// GetNextWarningNumberSequence provides a mock function with given fields: tx
func (_m *MockDao) GetNextWarningNumberSequence(tx ...*gorm.DB) (string, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetNextWarningNumberSequence")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) (string, error)); ok {
		return rf(tx...)
	}
	if rf, ok := ret.Get(0).(func(...*gorm.DB) string); ok {
		r0 = rf(tx...)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(...*gorm.DB) error); ok {
		r1 = rf(tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetNextWarningNumberSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNextWarningNumberSequence'
type MockDao_GetNextWarningNumberSequence_Call struct {
	*mock.Call
}

// GetNextWarningNumberSequence is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetNextWarningNumberSequence(tx ...interface{}) *MockDao_GetNextWarningNumberSequence_Call {
	return &MockDao_GetNextWarningNumberSequence_Call{Call: _e.mock.On("GetNextWarningNumberSequence",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_GetNextWarningNumberSequence_Call) Run(run func(tx ...*gorm.DB)) *MockDao_GetNextWarningNumberSequence_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetNextWarningNumberSequence_Call) Return(_a0 string, _a1 error) *MockDao_GetNextWarningNumberSequence_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetNextWarningNumberSequence_Call) RunAndReturn(run func(...*gorm.DB) (string, error)) *MockDao_GetNextWarningNumberSequence_Call {
	_c.Call.Return(run)
	return _c
}

// GetNotificationSettingByUserId provides a mock function with given fields: userId, tx
func (_m *MockDao) GetNotificationSettingByUserId(userId uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, userId)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetNotificationSettingByUserId")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modnotif.NotificationSetting, error)); ok {
		return rf(userId, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modnotif.NotificationSetting); ok {
		r0 = rf(userId, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(userId, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetNotificationSettingByUserId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetNotificationSettingByUserId'
type MockDao_GetNotificationSettingByUserId_Call struct {
	*mock.Call
}

// GetNotificationSettingByUserId is a helper method to define mock.On call
//   - userId uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetNotificationSettingByUserId(userId interface{}, tx ...interface{}) *MockDao_GetNotificationSettingByUserId_Call {
	return &MockDao_GetNotificationSettingByUserId_Call{Call: _e.mock.On("GetNotificationSettingByUserId",
		append([]interface{}{userId}, tx...)...)}
}

func (_c *MockDao_GetNotificationSettingByUserId_Call) Run(run func(userId uuid.UUID, tx ...*gorm.DB)) *MockDao_GetNotificationSettingByUserId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetNotificationSettingByUserId_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockDao_GetNotificationSettingByUserId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetNotificationSettingByUserId_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modnotif.NotificationSetting, error)) *MockDao_GetNotificationSettingByUserId_Call {
	_c.Call.Return(run)
	return _c
}

// GetWarningForCode provides a mock function with given fields: code, tx
func (_m *MockDao) GetWarningForCode(code cjson.JSONB, tx ...*gorm.DB) (*modwarn.Warning, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, code)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetWarningForCode")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(cjson.JSONB, ...*gorm.DB) (*modwarn.Warning, error)); ok {
		return rf(code, tx...)
	}
	if rf, ok := ret.Get(0).(func(cjson.JSONB, ...*gorm.DB) *modwarn.Warning); ok {
		r0 = rf(code, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(cjson.JSONB, ...*gorm.DB) error); ok {
		r1 = rf(code, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_GetWarningForCode_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetWarningForCode'
type MockDao_GetWarningForCode_Call struct {
	*mock.Call
}

// GetWarningForCode is a helper method to define mock.On call
//   - code cjson.JSONB
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) GetWarningForCode(code interface{}, tx ...interface{}) *MockDao_GetWarningForCode_Call {
	return &MockDao_GetWarningForCode_Call{Call: _e.mock.On("GetWarningForCode",
		append([]interface{}{code}, tx...)...)}
}

func (_c *MockDao_GetWarningForCode_Call) Run(run func(code cjson.JSONB, tx ...*gorm.DB)) *MockDao_GetWarningForCode_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(cjson.JSONB), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_GetWarningForCode_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockDao_GetWarningForCode_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_GetWarningForCode_Call) RunAndReturn(run func(cjson.JSONB, ...*gorm.DB) (*modwarn.Warning, error)) *MockDao_GetWarningForCode_Call {
	_c.Call.Return(run)
	return _c
}

// ResetWarningNumberSequence provides a mock function with given fields: tx
func (_m *MockDao) ResetWarningNumberSequence(tx ...*gorm.DB) error {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ResetWarningNumberSequence")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(...*gorm.DB) error); ok {
		r0 = rf(tx...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDao_ResetWarningNumberSequence_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResetWarningNumberSequence'
type MockDao_ResetWarningNumberSequence_Call struct {
	*mock.Call
}

// ResetWarningNumberSequence is a helper method to define mock.On call
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) ResetWarningNumberSequence(tx ...interface{}) *MockDao_ResetWarningNumberSequence_Call {
	return &MockDao_ResetWarningNumberSequence_Call{Call: _e.mock.On("ResetWarningNumberSequence",
		append([]interface{}{}, tx...)...)}
}

func (_c *MockDao_ResetWarningNumberSequence_Call) Run(run func(tx ...*gorm.DB)) *MockDao_ResetWarningNumberSequence_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *MockDao_ResetWarningNumberSequence_Call) Return(_a0 error) *MockDao_ResetWarningNumberSequence_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *MockDao_ResetWarningNumberSequence_Call) RunAndReturn(run func(...*gorm.DB) error) *MockDao_ResetWarningNumberSequence_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveAffectedItem provides a mock function with given fields: identity, tx
func (_m *MockDao) RetrieveAffectedItem(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.AffectedItem, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modnotif.AffectedItem, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modnotif.AffectedItem); ok {
		r0 = rf(identity, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveAffectedItem'
type MockDao_RetrieveAffectedItem_Call struct {
	*mock.Call
}

// RetrieveAffectedItem is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveAffectedItem(identity interface{}, tx ...interface{}) *MockDao_RetrieveAffectedItem_Call {
	return &MockDao_RetrieveAffectedItem_Call{Call: _e.mock.On("RetrieveAffectedItem",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_RetrieveAffectedItem_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockDao_RetrieveAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveAffectedItem_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modnotif.AffectedItem, error)) *MockDao_RetrieveAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveAsset provides a mock function with given fields: id, tx
func (_m *MockDao) RetrieveAsset(id uuid.UUID, tx ...*gorm.DB) (*modinv.Asset, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modinv.Asset, error)); ok {
		return rf(id, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modinv.Asset); ok {
		r0 = rf(id, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(id, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveAsset'
type MockDao_RetrieveAsset_Call struct {
	*mock.Call
}

// RetrieveAsset is a helper method to define mock.On call
//   - id uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveAsset(id interface{}, tx ...interface{}) *MockDao_RetrieveAsset_Call {
	return &MockDao_RetrieveAsset_Call{Call: _e.mock.On("RetrieveAsset",
		append([]interface{}{id}, tx...)...)}
}

func (_c *MockDao_RetrieveAsset_Call) Run(run func(id uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockDao_RetrieveAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveAsset_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modinv.Asset, error)) *MockDao_RetrieveAsset_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveLocation provides a mock function with given fields: identity, tx
func (_m *MockDao) RetrieveLocation(identity uuid.UUID, tx ...*gorm.DB) (*modinv.Location, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modinv.Location, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modinv.Location); ok {
		r0 = rf(identity, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveLocation'
type MockDao_RetrieveLocation_Call struct {
	*mock.Call
}

// RetrieveLocation is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveLocation(identity interface{}, tx ...interface{}) *MockDao_RetrieveLocation_Call {
	return &MockDao_RetrieveLocation_Call{Call: _e.mock.On("RetrieveLocation",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_RetrieveLocation_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockDao_RetrieveLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveLocation_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modinv.Location, error)) *MockDao_RetrieveLocation_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationQueue provides a mock function with given fields: id, tx
func (_m *MockDao) RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity, tx ...*gorm.DB) (*modnotif.NotificationQueue, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationQueue")
	}

	var r0 *modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity, ...*gorm.DB) (*modnotif.NotificationQueue, error)); ok {
		return rf(id, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueueIdentity, ...*gorm.DB) *modnotif.NotificationQueue); ok {
		r0 = rf(id, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueueIdentity, ...*gorm.DB) error); ok {
		r1 = rf(id, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationQueue'
type MockDao_RetrieveNotificationQueue_Call struct {
	*mock.Call
}

// RetrieveNotificationQueue is a helper method to define mock.On call
//   - id modnotif.NotificationQueueIdentity
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveNotificationQueue(id interface{}, tx ...interface{}) *MockDao_RetrieveNotificationQueue_Call {
	return &MockDao_RetrieveNotificationQueue_Call{Call: _e.mock.On("RetrieveNotificationQueue",
		append([]interface{}{id}, tx...)...)}
}

func (_c *MockDao_RetrieveNotificationQueue_Call) Run(run func(id modnotif.NotificationQueueIdentity, tx ...*gorm.DB)) *MockDao_RetrieveNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationQueueIdentity), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveNotificationQueue_Call) Return(_a0 *modnotif.NotificationQueue, _a1 error) *MockDao_RetrieveNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueueIdentity, ...*gorm.DB) (*modnotif.NotificationQueue, error)) *MockDao_RetrieveNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationRule provides a mock function with given fields: identity, tx
func (_m *MockDao) RetrieveNotificationRule(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationRule, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modnotif.NotificationRule, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modnotif.NotificationRule); ok {
		r0 = rf(identity, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationRule'
type MockDao_RetrieveNotificationRule_Call struct {
	*mock.Call
}

// RetrieveNotificationRule is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveNotificationRule(identity interface{}, tx ...interface{}) *MockDao_RetrieveNotificationRule_Call {
	return &MockDao_RetrieveNotificationRule_Call{Call: _e.mock.On("RetrieveNotificationRule",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_RetrieveNotificationRule_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockDao_RetrieveNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveNotificationRule_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modnotif.NotificationRule, error)) *MockDao_RetrieveNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveNotificationSetting provides a mock function with given fields: identity, tx
func (_m *MockDao) RetrieveNotificationSetting(identity uuid.UUID, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modnotif.NotificationSetting, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modnotif.NotificationSetting); ok {
		r0 = rf(identity, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveNotificationSetting'
type MockDao_RetrieveNotificationSetting_Call struct {
	*mock.Call
}

// RetrieveNotificationSetting is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveNotificationSetting(identity interface{}, tx ...interface{}) *MockDao_RetrieveNotificationSetting_Call {
	return &MockDao_RetrieveNotificationSetting_Call{Call: _e.mock.On("RetrieveNotificationSetting",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_RetrieveNotificationSetting_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockDao_RetrieveNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveNotificationSetting_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modnotif.NotificationSetting, error)) *MockDao_RetrieveNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveUser provides a mock function with given fields: id, tx
func (_m *MockDao) RetrieveUser(id uuid.UUID, tx ...*gorm.DB) (*modusr.User, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, id)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modusr.User, error)); ok {
		return rf(id, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modusr.User); ok {
		r0 = rf(id, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(id, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveUser'
type MockDao_RetrieveUser_Call struct {
	*mock.Call
}

// RetrieveUser is a helper method to define mock.On call
//   - id uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveUser(id interface{}, tx ...interface{}) *MockDao_RetrieveUser_Call {
	return &MockDao_RetrieveUser_Call{Call: _e.mock.On("RetrieveUser",
		append([]interface{}{id}, tx...)...)}
}

func (_c *MockDao_RetrieveUser_Call) Run(run func(id uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveUser_Call) Return(_a0 *modusr.User, _a1 error) *MockDao_RetrieveUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveUser_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modusr.User, error)) *MockDao_RetrieveUser_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveWarning provides a mock function with given fields: identity, tx
func (_m *MockDao) RetrieveWarning(identity uuid.UUID, tx ...*gorm.DB) (*modwarn.Warning, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modwarn.Warning, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modwarn.Warning); ok {
		r0 = rf(identity, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveWarning'
type MockDao_RetrieveWarning_Call struct {
	*mock.Call
}

// RetrieveWarning is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveWarning(identity interface{}, tx ...interface{}) *MockDao_RetrieveWarning_Call {
	return &MockDao_RetrieveWarning_Call{Call: _e.mock.On("RetrieveWarning",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_RetrieveWarning_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockDao_RetrieveWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveWarning_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modwarn.Warning, error)) *MockDao_RetrieveWarning_Call {
	_c.Call.Return(run)
	return _c
}

// RetrieveWarningHistory provides a mock function with given fields: identity, tx
func (_m *MockDao) RetrieveWarningHistory(identity uuid.UUID, tx ...*gorm.DB) (*modwarn.WarningHistory, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, identity)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for RetrieveWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) (*modwarn.WarningHistory, error)); ok {
		return rf(identity, tx...)
	}
	if rf, ok := ret.Get(0).(func(uuid.UUID, ...*gorm.DB) *modwarn.WarningHistory); ok {
		r0 = rf(identity, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(uuid.UUID, ...*gorm.DB) error); ok {
		r1 = rf(identity, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_RetrieveWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'RetrieveWarningHistory'
type MockDao_RetrieveWarningHistory_Call struct {
	*mock.Call
}

// RetrieveWarningHistory is a helper method to define mock.On call
//   - identity uuid.UUID
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) RetrieveWarningHistory(identity interface{}, tx ...interface{}) *MockDao_RetrieveWarningHistory_Call {
	return &MockDao_RetrieveWarningHistory_Call{Call: _e.mock.On("RetrieveWarningHistory",
		append([]interface{}{identity}, tx...)...)}
}

func (_c *MockDao_RetrieveWarningHistory_Call) Run(run func(identity uuid.UUID, tx ...*gorm.DB)) *MockDao_RetrieveWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(uuid.UUID), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_RetrieveWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockDao_RetrieveWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_RetrieveWarningHistory_Call) RunAndReturn(run func(uuid.UUID, ...*gorm.DB) (*modwarn.WarningHistory, error)) *MockDao_RetrieveWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// Transaction provides a mock function with given fields: fc, opts
func (_m *MockDao) Transaction(fc func(*gorm.DB) error, opts ...*sql.TxOptions) error {
	_va := make([]interface{}, len(opts))
	for _i := range opts {
		_va[_i] = opts[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, fc)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for Transaction")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(func(*gorm.DB) error, ...*sql.TxOptions) error); ok {
		r0 = rf(fc, opts...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockDao_Transaction_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Transaction'
type MockDao_Transaction_Call struct {
	*mock.Call
}

// Transaction is a helper method to define mock.On call
//   - fc func(*gorm.DB) error
//   - opts ...*sql.TxOptions
func (_e *MockDao_Expecter) Transaction(fc interface{}, opts ...interface{}) *MockDao_Transaction_Call {
	return &MockDao_Transaction_Call{Call: _e.mock.On("Transaction",
		append([]interface{}{fc}, opts...)...)}
}

func (_c *MockDao_Transaction_Call) Run(run func(fc func(*gorm.DB) error, opts ...*sql.TxOptions)) *MockDao_Transaction_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*sql.TxOptions, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*sql.TxOptions)
			}
		}
		run(args[0].(func(*gorm.DB) error), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_Transaction_Call) Return(err error) *MockDao_Transaction_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDao_Transaction_Call) RunAndReturn(run func(func(*gorm.DB) error, ...*sql.TxOptions) error) *MockDao_Transaction_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAffectedItem provides a mock function with given fields: item, tx
func (_m *MockDao) UpdateAffectedItem(item modnotif.AffectedItem, tx ...*gorm.DB) (*modnotif.AffectedItem, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, item)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAffectedItem")
	}

	var r0 *modnotif.AffectedItem
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem, ...*gorm.DB) (*modnotif.AffectedItem, error)); ok {
		return rf(item, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.AffectedItem, ...*gorm.DB) *modnotif.AffectedItem); ok {
		r0 = rf(item, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.AffectedItem)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.AffectedItem, ...*gorm.DB) error); ok {
		r1 = rf(item, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateAffectedItem_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAffectedItem'
type MockDao_UpdateAffectedItem_Call struct {
	*mock.Call
}

// UpdateAffectedItem is a helper method to define mock.On call
//   - item modnotif.AffectedItem
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateAffectedItem(item interface{}, tx ...interface{}) *MockDao_UpdateAffectedItem_Call {
	return &MockDao_UpdateAffectedItem_Call{Call: _e.mock.On("UpdateAffectedItem",
		append([]interface{}{item}, tx...)...)}
}

func (_c *MockDao_UpdateAffectedItem_Call) Run(run func(item modnotif.AffectedItem, tx ...*gorm.DB)) *MockDao_UpdateAffectedItem_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.AffectedItem), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateAffectedItem_Call) Return(_a0 *modnotif.AffectedItem, _a1 error) *MockDao_UpdateAffectedItem_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateAffectedItem_Call) RunAndReturn(run func(modnotif.AffectedItem, ...*gorm.DB) (*modnotif.AffectedItem, error)) *MockDao_UpdateAffectedItem_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateAsset provides a mock function with given fields: asset, tx
func (_m *MockDao) UpdateAsset(asset modinv.Asset, tx ...*gorm.DB) (*modinv.Asset, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, asset)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateAsset")
	}

	var r0 *modinv.Asset
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Asset, ...*gorm.DB) (*modinv.Asset, error)); ok {
		return rf(asset, tx...)
	}
	if rf, ok := ret.Get(0).(func(modinv.Asset, ...*gorm.DB) *modinv.Asset); ok {
		r0 = rf(asset, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Asset)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Asset, ...*gorm.DB) error); ok {
		r1 = rf(asset, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateAsset_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateAsset'
type MockDao_UpdateAsset_Call struct {
	*mock.Call
}

// UpdateAsset is a helper method to define mock.On call
//   - asset modinv.Asset
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateAsset(asset interface{}, tx ...interface{}) *MockDao_UpdateAsset_Call {
	return &MockDao_UpdateAsset_Call{Call: _e.mock.On("UpdateAsset",
		append([]interface{}{asset}, tx...)...)}
}

func (_c *MockDao_UpdateAsset_Call) Run(run func(asset modinv.Asset, tx ...*gorm.DB)) *MockDao_UpdateAsset_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modinv.Asset), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateAsset_Call) Return(_a0 *modinv.Asset, _a1 error) *MockDao_UpdateAsset_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateAsset_Call) RunAndReturn(run func(modinv.Asset, ...*gorm.DB) (*modinv.Asset, error)) *MockDao_UpdateAsset_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateLocation provides a mock function with given fields: location, tx
func (_m *MockDao) UpdateLocation(location modinv.Location, tx ...*gorm.DB) (*modinv.Location, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, location)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateLocation")
	}

	var r0 *modinv.Location
	var r1 error
	if rf, ok := ret.Get(0).(func(modinv.Location, ...*gorm.DB) (*modinv.Location, error)); ok {
		return rf(location, tx...)
	}
	if rf, ok := ret.Get(0).(func(modinv.Location, ...*gorm.DB) *modinv.Location); ok {
		r0 = rf(location, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modinv.Location)
		}
	}

	if rf, ok := ret.Get(1).(func(modinv.Location, ...*gorm.DB) error); ok {
		r1 = rf(location, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateLocation_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateLocation'
type MockDao_UpdateLocation_Call struct {
	*mock.Call
}

// UpdateLocation is a helper method to define mock.On call
//   - location modinv.Location
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateLocation(location interface{}, tx ...interface{}) *MockDao_UpdateLocation_Call {
	return &MockDao_UpdateLocation_Call{Call: _e.mock.On("UpdateLocation",
		append([]interface{}{location}, tx...)...)}
}

func (_c *MockDao_UpdateLocation_Call) Run(run func(location modinv.Location, tx ...*gorm.DB)) *MockDao_UpdateLocation_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modinv.Location), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateLocation_Call) Return(_a0 *modinv.Location, _a1 error) *MockDao_UpdateLocation_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateLocation_Call) RunAndReturn(run func(modinv.Location, ...*gorm.DB) (*modinv.Location, error)) *MockDao_UpdateLocation_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationQueue provides a mock function with given fields: notificationQueue, tx
func (_m *MockDao) UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB) (*modnotif.NotificationQueue, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, notificationQueue)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationQueue")
	}

	var r0 *modnotif.NotificationQueue
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue, ...*gorm.DB) (*modnotif.NotificationQueue, error)); ok {
		return rf(notificationQueue, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationQueue, ...*gorm.DB) *modnotif.NotificationQueue); ok {
		r0 = rf(notificationQueue, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationQueue)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationQueue, ...*gorm.DB) error); ok {
		r1 = rf(notificationQueue, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateNotificationQueue_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationQueue'
type MockDao_UpdateNotificationQueue_Call struct {
	*mock.Call
}

// UpdateNotificationQueue is a helper method to define mock.On call
//   - notificationQueue modnotif.NotificationQueue
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateNotificationQueue(notificationQueue interface{}, tx ...interface{}) *MockDao_UpdateNotificationQueue_Call {
	return &MockDao_UpdateNotificationQueue_Call{Call: _e.mock.On("UpdateNotificationQueue",
		append([]interface{}{notificationQueue}, tx...)...)}
}

func (_c *MockDao_UpdateNotificationQueue_Call) Run(run func(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB)) *MockDao_UpdateNotificationQueue_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationQueue), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateNotificationQueue_Call) Return(_a0 *modnotif.NotificationQueue, _a1 error) *MockDao_UpdateNotificationQueue_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateNotificationQueue_Call) RunAndReturn(run func(modnotif.NotificationQueue, ...*gorm.DB) (*modnotif.NotificationQueue, error)) *MockDao_UpdateNotificationQueue_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationRule provides a mock function with given fields: rule, tx
func (_m *MockDao) UpdateNotificationRule(rule modnotif.NotificationRule, tx ...*gorm.DB) (*modnotif.NotificationRule, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, rule)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationRule")
	}

	var r0 *modnotif.NotificationRule
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule, ...*gorm.DB) (*modnotif.NotificationRule, error)); ok {
		return rf(rule, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationRule, ...*gorm.DB) *modnotif.NotificationRule); ok {
		r0 = rf(rule, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationRule)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationRule, ...*gorm.DB) error); ok {
		r1 = rf(rule, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateNotificationRule_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationRule'
type MockDao_UpdateNotificationRule_Call struct {
	*mock.Call
}

// UpdateNotificationRule is a helper method to define mock.On call
//   - rule modnotif.NotificationRule
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateNotificationRule(rule interface{}, tx ...interface{}) *MockDao_UpdateNotificationRule_Call {
	return &MockDao_UpdateNotificationRule_Call{Call: _e.mock.On("UpdateNotificationRule",
		append([]interface{}{rule}, tx...)...)}
}

func (_c *MockDao_UpdateNotificationRule_Call) Run(run func(rule modnotif.NotificationRule, tx ...*gorm.DB)) *MockDao_UpdateNotificationRule_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationRule), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateNotificationRule_Call) Return(_a0 *modnotif.NotificationRule, _a1 error) *MockDao_UpdateNotificationRule_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateNotificationRule_Call) RunAndReturn(run func(modnotif.NotificationRule, ...*gorm.DB) (*modnotif.NotificationRule, error)) *MockDao_UpdateNotificationRule_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateNotificationSetting provides a mock function with given fields: setting, tx
func (_m *MockDao) UpdateNotificationSetting(setting modnotif.NotificationSetting, tx ...*gorm.DB) (*modnotif.NotificationSetting, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, setting)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotificationSetting")
	}

	var r0 *modnotif.NotificationSetting
	var r1 error
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting, ...*gorm.DB) (*modnotif.NotificationSetting, error)); ok {
		return rf(setting, tx...)
	}
	if rf, ok := ret.Get(0).(func(modnotif.NotificationSetting, ...*gorm.DB) *modnotif.NotificationSetting); ok {
		r0 = rf(setting, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modnotif.NotificationSetting)
		}
	}

	if rf, ok := ret.Get(1).(func(modnotif.NotificationSetting, ...*gorm.DB) error); ok {
		r1 = rf(setting, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateNotificationSetting_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateNotificationSetting'
type MockDao_UpdateNotificationSetting_Call struct {
	*mock.Call
}

// UpdateNotificationSetting is a helper method to define mock.On call
//   - setting modnotif.NotificationSetting
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateNotificationSetting(setting interface{}, tx ...interface{}) *MockDao_UpdateNotificationSetting_Call {
	return &MockDao_UpdateNotificationSetting_Call{Call: _e.mock.On("UpdateNotificationSetting",
		append([]interface{}{setting}, tx...)...)}
}

func (_c *MockDao_UpdateNotificationSetting_Call) Run(run func(setting modnotif.NotificationSetting, tx ...*gorm.DB)) *MockDao_UpdateNotificationSetting_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modnotif.NotificationSetting), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateNotificationSetting_Call) Return(_a0 *modnotif.NotificationSetting, _a1 error) *MockDao_UpdateNotificationSetting_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateNotificationSetting_Call) RunAndReturn(run func(modnotif.NotificationSetting, ...*gorm.DB) (*modnotif.NotificationSetting, error)) *MockDao_UpdateNotificationSetting_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateUser provides a mock function with given fields: user, tx
func (_m *MockDao) UpdateUser(user modusr.User, tx ...*gorm.DB) (*modusr.User, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, user)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateUser")
	}

	var r0 *modusr.User
	var r1 error
	if rf, ok := ret.Get(0).(func(modusr.User, ...*gorm.DB) (*modusr.User, error)); ok {
		return rf(user, tx...)
	}
	if rf, ok := ret.Get(0).(func(modusr.User, ...*gorm.DB) *modusr.User); ok {
		r0 = rf(user, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modusr.User)
		}
	}

	if rf, ok := ret.Get(1).(func(modusr.User, ...*gorm.DB) error); ok {
		r1 = rf(user, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateUser_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateUser'
type MockDao_UpdateUser_Call struct {
	*mock.Call
}

// UpdateUser is a helper method to define mock.On call
//   - user modusr.User
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateUser(user interface{}, tx ...interface{}) *MockDao_UpdateUser_Call {
	return &MockDao_UpdateUser_Call{Call: _e.mock.On("UpdateUser",
		append([]interface{}{user}, tx...)...)}
}

func (_c *MockDao_UpdateUser_Call) Run(run func(user modusr.User, tx ...*gorm.DB)) *MockDao_UpdateUser_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modusr.User), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateUser_Call) Return(_a0 *modusr.User, _a1 error) *MockDao_UpdateUser_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateUser_Call) RunAndReturn(run func(modusr.User, ...*gorm.DB) (*modusr.User, error)) *MockDao_UpdateUser_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarning provides a mock function with given fields: warning, tx
func (_m *MockDao) UpdateWarning(warning modwarn.Warning, tx ...*gorm.DB) (*modwarn.Warning, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warning)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarning")
	}

	var r0 *modwarn.Warning
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.Warning, ...*gorm.DB) (*modwarn.Warning, error)); ok {
		return rf(warning, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.Warning, ...*gorm.DB) *modwarn.Warning); ok {
		r0 = rf(warning, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.Warning)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.Warning, ...*gorm.DB) error); ok {
		r1 = rf(warning, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateWarning_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarning'
type MockDao_UpdateWarning_Call struct {
	*mock.Call
}

// UpdateWarning is a helper method to define mock.On call
//   - warning modwarn.Warning
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateWarning(warning interface{}, tx ...interface{}) *MockDao_UpdateWarning_Call {
	return &MockDao_UpdateWarning_Call{Call: _e.mock.On("UpdateWarning",
		append([]interface{}{warning}, tx...)...)}
}

func (_c *MockDao_UpdateWarning_Call) Run(run func(warning modwarn.Warning, tx ...*gorm.DB)) *MockDao_UpdateWarning_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.Warning), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateWarning_Call) Return(_a0 *modwarn.Warning, _a1 error) *MockDao_UpdateWarning_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateWarning_Call) RunAndReturn(run func(modwarn.Warning, ...*gorm.DB) (*modwarn.Warning, error)) *MockDao_UpdateWarning_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateWarningHistory provides a mock function with given fields: warningHistory, tx
func (_m *MockDao) UpdateWarningHistory(warningHistory modwarn.WarningHistory, tx ...*gorm.DB) (*modwarn.WarningHistory, error) {
	_va := make([]interface{}, len(tx))
	for _i := range tx {
		_va[_i] = tx[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, warningHistory)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateWarningHistory")
	}

	var r0 *modwarn.WarningHistory
	var r1 error
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory, ...*gorm.DB) (*modwarn.WarningHistory, error)); ok {
		return rf(warningHistory, tx...)
	}
	if rf, ok := ret.Get(0).(func(modwarn.WarningHistory, ...*gorm.DB) *modwarn.WarningHistory); ok {
		r0 = rf(warningHistory, tx...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*modwarn.WarningHistory)
		}
	}

	if rf, ok := ret.Get(1).(func(modwarn.WarningHistory, ...*gorm.DB) error); ok {
		r1 = rf(warningHistory, tx...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// MockDao_UpdateWarningHistory_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateWarningHistory'
type MockDao_UpdateWarningHistory_Call struct {
	*mock.Call
}

// UpdateWarningHistory is a helper method to define mock.On call
//   - warningHistory modwarn.WarningHistory
//   - tx ...*gorm.DB
func (_e *MockDao_Expecter) UpdateWarningHistory(warningHistory interface{}, tx ...interface{}) *MockDao_UpdateWarningHistory_Call {
	return &MockDao_UpdateWarningHistory_Call{Call: _e.mock.On("UpdateWarningHistory",
		append([]interface{}{warningHistory}, tx...)...)}
}

func (_c *MockDao_UpdateWarningHistory_Call) Run(run func(warningHistory modwarn.WarningHistory, tx ...*gorm.DB)) *MockDao_UpdateWarningHistory_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]*gorm.DB, len(args)-1)
		for i, a := range args[1:] {
			if a != nil {
				variadicArgs[i] = a.(*gorm.DB)
			}
		}
		run(args[0].(modwarn.WarningHistory), variadicArgs...)
	})
	return _c
}

func (_c *MockDao_UpdateWarningHistory_Call) Return(_a0 *modwarn.WarningHistory, _a1 error) *MockDao_UpdateWarningHistory_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *MockDao_UpdateWarningHistory_Call) RunAndReturn(run func(modwarn.WarningHistory, ...*gorm.DB) (*modwarn.WarningHistory, error)) *MockDao_UpdateWarningHistory_Call {
	_c.Call.Return(run)
	return _c
}

// NewMockDao creates a new instance of MockDao. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDao(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDao {
	mock := &MockDao{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
