package db

import (
	"regexp"

	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

var (
	expectedNotificationQueueColumns = []string{"user_id", "notification_rule_id", "warning_id", "frequency"}
)

func (s *DaoUnitTestSuite) TestNotificationQueueDao_GetAllNotificationQueues() {
	// given
	expected := modnotif.AllMockNotificationQueues(s.T())

	s.Mock().ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "notification_queues"`)).
		WillReturnRows(
			sqlmock.NewRows(expectedNotificationQueueColumns).
				AddRow(
					expected[0].UserId,
					expected[0].NotificationRuleId,
					expected[0].WarningId,
					expected[0].Frequency,
				).
				AddRow(
					expected[1].UserId,
					expected[1].NotificationRuleId,
					expected[1].WarningId,
					expected[1].Frequency,
				))

	notificationQueueDao := newDao(s.GetPostgresDb())

	// when
	result, err := notificationQueueDao.GetAllNotificationQueues()

	// then
	require.NoError(s.T(), err)
	assert.True(s.T(), lo.Every(expected, result))
}

func (s *DaoUnitTestSuite) TestNotificationQueueDao_RetrieveNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())

	s.Mock().ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "notification_queues" WHERE user_id = $1 AND notification_rule_id = $2 AND warning_id = $3 ORDER BY "notification_queues"."user_id" LIMIT $4`)).
		WithArgs(
			expected.UserId,
			expected.NotificationRuleId,
			expected.WarningId,
			1,
		).
		WillReturnRows(
			sqlmock.NewRows(expectedNotificationQueueColumns).
				AddRow(
					expected.UserId,
					expected.NotificationRuleId,
					expected.WarningId,
					expected.Frequency,
				))

	notificationQueueDao := newDao(s.GetPostgresDb())

	// when
	result, err := notificationQueueDao.RetrieveNotificationQueue(expected.GetIdentity())

	// then
	require.NoError(s.T(), err)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoUnitTestSuite) TestNotificationQueueDao_GetAllNotificationQueuesForUserAndFrequency() {
	// given
	expected := []modnotif.NotificationQueue{*modnotif.MockNotificationQueue1(s.T())}

	s.Mock().ExpectQuery(regexp.QuoteMeta(`SELECT * FROM "notification_queues" WHERE user_id = $1 AND frequency = $2`)).
		WithArgs(
			expected[0].UserId,
			expected[0].Frequency,
		).
		WillReturnRows(
			sqlmock.NewRows(expectedNotificationQueueColumns).
				AddRow(
					expected[0].UserId,
					expected[0].NotificationRuleId,
					expected[0].WarningId,
					expected[0].Frequency,
				))

	notificationQueueDao := newDao(s.GetPostgresDb())

	// when
	result, err := notificationQueueDao.GetAllNotificationQueuesForUserAndFrequency(expected[0].GetIdentity().UserId, expected[0].Frequency)

	// then
	require.NoError(s.T(), err)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoUnitTestSuite) TestNotificationQueueDao_CreateNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())

	s.Mock().ExpectBegin()
	s.Mock().ExpectExec(regexp.QuoteMeta(`INSERT INTO "notification_queues" ("user_id","notification_rule_id","warning_id","frequency") VALUES ($1,$2,$3,$4)`)).
		WithArgs(
			expected.UserId,
			expected.NotificationRuleId,
			expected.WarningId,
			expected.Frequency,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.Mock().ExpectCommit()

	notificationQueueDao := newDao(s.GetPostgresDb())

	// when
	result, err := notificationQueueDao.CreateNotificationQueue(*expected)

	// then
	require.NoError(s.T(), err)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoUnitTestSuite) TestNotificationQueueDao_UpdateNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())

	s.Mock().ExpectBegin()
	s.Mock().ExpectExec(regexp.QuoteMeta(`UPDATE "notification_queues" SET "frequency"=$1 WHERE "user_id" = $2 AND "notification_rule_id" = $3 AND "warning_id" = $4`)).
		WithArgs(
			expected.Frequency,
			expected.UserId,
			expected.NotificationRuleId,
			expected.WarningId,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.Mock().ExpectCommit()

	notificationQueueDao := newDao(s.GetPostgresDb())

	// when
	result, err := notificationQueueDao.UpdateNotificationQueue(*expected)

	// then
	require.NoError(s.T(), err)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoUnitTestSuite) TestNotificationQueueDao_DeleteNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())

	s.Mock().ExpectBegin()
	s.Mock().ExpectExec(regexp.QuoteMeta(`DELETE FROM "notification_queues" WHERE user_id = $1 AND notification_rule_id = $2 AND warning_id = $3`)).
		WithArgs(
			expected.UserId,
			expected.NotificationRuleId,
			expected.WarningId,
		).
		WillReturnResult(sqlmock.NewResult(1, 1))
	s.Mock().ExpectCommit()

	notificationQueueDao := newDao(s.GetPostgresDb())

	// when
	result, err := notificationQueueDao.DeleteNotificationQueue(expected.GetIdentity())

	// then
	require.NoError(s.T(), err)
	assert.True(s.T(), result)
}
