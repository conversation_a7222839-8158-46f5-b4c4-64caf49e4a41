//go:build !compile

package db

import (
	"github.com/CyberOwlTeam/go-utilities/pkg/ctutils/ctdb/ctmdb"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modcfg"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modusr"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/stretchr/testify/require"
	"testing"
)

type DaoIntegrationTestSuite struct {
	*ctmdb.MultiDbContainersIntegrationTestSuite
}

func NewDaoIntegrationTestSuite(t testing.TB, cfg *modcfg.MainConfig, params ...string) *DaoIntegrationTestSuite {
	var err error

	prefix := "was-db"
	flywayPath := "../../../deployment/db/flyway/sql"
	if len(params) == 2 {
		prefix = params[0]
		flywayPath = params[1]
	}

	networkContainer := ctmdb.NewMultiDbNetwork()
	postgresContainerCfg := ctmdb.NewMultiPostgresContainerConfig(cfg.Db)
	err = postgresContainerCfg.Validate()
	if err != nil {
		require.NoError(t, err)
	}

	postgresContainerParams := ctmdb.NewMultiPostgresContainerParamsWithFlywayPath(prefix, flywayPath)
	postgresContainer := ctmdb.NewMultiPostgresDbContainer(postgresContainerCfg, postgresContainerParams, networkContainer)

	s := ctmdb.NewMultiDbContainersIntegrationTestSuite(ctmdb.NewMultiDbContainers().
		WithNetworkContainer(networkContainer).
		WithDbContainer(postgresContainer))

	return &DaoIntegrationTestSuite{
		MultiDbContainersIntegrationTestSuite: s,
	}
}

func (s *DaoIntegrationTestSuite) SetupLocations(locations ...modinv.Location) {
	dao := newDao(s.GetPostgresDb())
	if len(locations) == 0 {
		locations = modinv.AllMockLocations(s.T())
	}

	for _, location := range locations {
		_, err := dao.CreateLocation(location)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupAssets(assets ...modinv.Asset) {
	dao := newDao(s.GetPostgresDb())
	if len(assets) == 0 {
		assets = modinv.AllMockAssets(s.T())
	}

	for _, asset := range assets {
		_, err := dao.CreateAsset(asset)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupNotificationRules(notifRules ...modnotif.NotificationRule) {
	dao := newDao(s.GetPostgresDb())
	if len(notifRules) == 0 {
		notifRules = modnotif.AllMockNotificationRules(s.T())
	}

	for _, notifRule := range notifRules {
		_, err := dao.CreateNotificationRule(notifRule)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupNotificationSettings(notifSettings ...modnotif.NotificationSetting) {
	dao := newDao(s.GetPostgresDb())
	if len(notifSettings) == 0 {
		notifSettings = modnotif.AllMockNotificationSettings(s.T())
	}

	for _, notifSetting := range notifSettings {
		_, err := dao.CreateNotificationSetting(notifSetting)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupWarnings(warnings ...modwarn.Warning) {
	dao := newDao(s.GetPostgresDb())
	if len(warnings) == 0 {
		warnings = modwarn.AllMockWarnings(s.T())
	}

	err := dao.ResetWarningNumberSequence() // reset sequence so other tests don't increment it
	require.NoError(s.T(), err)
	for _, warning := range warnings {
		_, err := dao.CreateWarning(warning)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupWarningsHistory(warningsHistory ...modwarn.WarningHistory) {
	dao := newDao(s.GetPostgresDb())
	if len(warningsHistory) == 0 {
		warningsHistory = modwarn.AllMockWarningsHistory(s.T())
	}

	for _, wh := range warningsHistory {
		_, err := dao.CreateWarningHistory(wh)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupUsers(users ...modusr.User) {
	dao := newDao(s.GetPostgresDb())
	if len(users) == 0 {
		users = modusr.AllMockUsers(s.T())
	}

	for _, user := range users {
		_, err := dao.CreateUser(user)
		require.NoError(s.T(), err)
	}
}

func (s *DaoIntegrationTestSuite) SetupNotificationQueues(notificationQueues ...modnotif.NotificationQueue) {
	dao := newDao(s.GetPostgresDb())
	if len(notificationQueues) == 0 {
		notificationQueues = modnotif.AllMockNotificationQueues(s.T())
	}

	for _, nq := range notificationQueues {
		_, err := dao.CreateNotificationQueue(nq)
		require.NoError(s.T(), err)
	}
}
