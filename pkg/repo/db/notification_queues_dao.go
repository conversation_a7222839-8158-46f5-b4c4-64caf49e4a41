package db

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type notificationQueuesDao interface {
	GetAllNotificationQueues(tx ...*gorm.DB) ([]modnotif.NotificationQueue, error)
	RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity, tx ...*gorm.DB) (*modnotif.NotificationQueue, error)
	GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency, tx ...*gorm.DB) ([]modnotif.NotificationQueue, error)
	CreateNotificationQueue(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB) (*modnotif.NotificationQueue, error)
	UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB) (*modnotif.NotificationQueue, error)
	DeleteNotificationQueue(id modnotif.NotificationQueueIdentity, tx ...*gorm.DB) (bool, error)
}

func (d dao) GetAllNotificationQueues(tx ...*gorm.DB) ([]modnotif.NotificationQueue, error) {
	var notificationQueues []modnotif.NotificationQueue

	err := d.getDb(tx...).
		Table("notification_queues").
		Find(&notificationQueues).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return notificationQueues, nil
}

func (d dao) GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency, tx ...*gorm.DB) ([]modnotif.NotificationQueue, error) {
	var notificationQueues []modnotif.NotificationQueue

	err := d.getDb(tx...).
		Table("notification_queues").
		Where("user_id = ? AND frequency = ?", user, frequency).
		Find(&notificationQueues).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return notificationQueues, nil
}

func (d dao) RetrieveNotificationQueue(nqId modnotif.NotificationQueueIdentity, tx ...*gorm.DB) (*modnotif.NotificationQueue, error) {
	var notificationQueue modnotif.NotificationQueue

	err := d.getDb(tx...).
		Table("notification_queues").
		Where("user_id = ? AND notification_rule_id = ? AND warning_id = ?", nqId.UserId, nqId.NotificationRuleId, nqId.WarningId).
		First(&notificationQueue).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &notificationQueue, err
}

func (d dao) CreateNotificationQueue(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB) (*modnotif.NotificationQueue, error) {
	err := d.getDb(tx...).
		Table("notification_queues").
		Create(&notificationQueue).
		Error
	if err != nil {
		return nil, err
	}

	return &notificationQueue, nil
}

func (d dao) UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue, tx ...*gorm.DB) (*modnotif.NotificationQueue, error) {
	err := d.getDb(tx...).
		Table("notification_queues").
		Updates(&notificationQueue).
		Error

	if err != nil {
		return nil, err
	}

	return &notificationQueue, nil
}

func (d dao) DeleteNotificationQueue(nqId modnotif.NotificationQueueIdentity, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Table("notification_queues").
		Where("user_id = ? AND notification_rule_id = ? AND warning_id = ?", nqId.UserId, nqId.NotificationRuleId, nqId.WarningId).
		Delete(&modnotif.NotificationQueue{}).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}
