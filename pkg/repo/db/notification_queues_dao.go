package db

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type notificationQueuesDao interface {
	GetAllNotificationQueues(tx ...*gorm.DB) ([]modinv.NotificationQueue, error)
	RetrieveNotificationQueue(id uuid.UUID, tx ...*gorm.DB) (*modinv.NotificationQueue, error)
	CreateNotificationQueue(notificationQueue modinv.NotificationQueue, tx ...*gorm.DB) (*modinv.NotificationQueue, error)
	UpdateNotificationQueue(notificationQueue modinv.NotificationQueue, tx ...*gorm.DB) (*modinv.NotificationQueue, error)
	DeleteNotificationQueue(id uuid.UUID, tx ...*gorm.DB) (bool, error)
	DeleteNotificationQueuesForLocation(locationId uuid.UUID, tx ...*gorm.DB) (bool, error)
}

func (d dao) GetAllNotificationQueues(tx ...*gorm.DB) ([]modinv.NotificationQueue, error) {
	var notificationQueues []modinv.NotificationQueue

	err := d.getDb(tx...).
		Table("notificationQueues").
		Find(&notificationQueues).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return notificationQueues, nil
}

func (d dao) RetrieveNotificationQueue(id uuid.UUID, tx ...*gorm.DB) (*modinv.NotificationQueue, error) {
	var notificationQueue modinv.NotificationQueue

	err := d.getDb(tx...).
		Table("notificationQueues").
		Where("id = ?", id).
		First(&notificationQueue).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	return &notificationQueue, err
}

func (d dao) CreateNotificationQueue(notificationQueue modinv.NotificationQueue, tx ...*gorm.DB) (*modinv.NotificationQueue, error) {
	err := d.getDb(tx...).
		Table("notificationQueues").
		Create(&notificationQueue).
		Error
	if err != nil {
		return nil, err
	}

	return &notificationQueue, nil
}

func (d dao) UpdateNotificationQueue(notificationQueue modinv.NotificationQueue, tx ...*gorm.DB) (*modinv.NotificationQueue, error) {
	err := d.getDb(tx...).
		Table("notificationQueues").
		Updates(&notificationQueue).
		Error

	if err != nil {
		return nil, err
	}

	return &notificationQueue, nil
}

func (d dao) DeleteNotificationQueue(id uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Table("notificationQueues").
		Delete(&modinv.NotificationQueue{}, id).
		Error

	if err != nil {
		return false, err
	}

	return true, err
}

func (d dao) DeleteNotificationQueuesForLocation(locationId uuid.UUID, tx ...*gorm.DB) (bool, error) {
	err := d.getDb(tx...).
		Table("notificationQueues").
		Where("location_id = ?", locationId).
		Delete(&modinv.NotificationQueue{}).
		Error

	if errors.Is(err, gorm.ErrRecordNotFound) {
		return false, nil
	} else if err != nil {
		return false, err
	}
	return true, nil
}
