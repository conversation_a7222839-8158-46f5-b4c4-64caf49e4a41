package db

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func (s *DaoIntegrationTestSuite) TestIntNotificationQueueDao_GetAllNotificationQueues() {
	// given
	expected := modnotif.AllMockNotificationQueues(s.T())
	s.SetupLocations()
	s.SetupUsers()
	s.SetupNotificationSettings()
	s.SetupWarnings()

	s.SetupNotificationQueues(expected...)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetAllNotificationQueues()

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationQueueDao_RetrieveNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())
	s.SetupLocations()
	s.SetupUsers()
	s.SetupNotificationSettings()
	s.SetupWarnings()

	s.SetupNotificationQueues(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.RetrieveNotificationQueue(expected.GetIdentity())

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationQueueDao_GetAllNotificationQueuesForUserAndFrequency() {
	// given
	expected := []modnotif.NotificationQueue{*modnotif.MockNotificationQueue1(s.T())}
	s.SetupLocations()
	s.SetupUsers()
	s.SetupNotificationSettings()
	s.SetupWarnings()

	s.SetupNotificationQueues()

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetAllNotificationQueuesForUserAndFrequency(expected[0].GetIdentity().UserId, expected[0].Frequency)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationQueueDao_CreateNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())
	s.SetupLocations()
	s.SetupUsers()
	s.SetupNotificationSettings()
	s.SetupWarnings()

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.CreateNotificationQueue(*expected)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)

	result, err = dao.RetrieveNotificationQueue(expected.GetIdentity())
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationQueueDao_UpdateNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())
	s.SetupLocations()
	s.SetupUsers()
	s.SetupNotificationSettings()
	s.SetupWarnings()

	s.SetupNotificationQueues(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.UpdateNotificationQueue(*expected)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)

	result, err = dao.RetrieveNotificationQueue(expected.GetIdentity())
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntNotificationQueueDao_DeleteNotificationQueue() {
	// given
	expected := modnotif.MockNotificationQueue1(s.T())
	s.SetupLocations()
	s.SetupUsers()
	s.SetupNotificationSettings()
	s.SetupWarnings()

	s.SetupNotificationQueues(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.DeleteNotificationQueue(expected.GetIdentity())

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Truef(s.T(), result, "notificationQueue was not deleted")

	notificationQueue, err := dao.RetrieveNotificationQueue(expected.GetIdentity())
	require.NoError(s.T(), err)
	assert.Nil(s.T(), notificationQueue)
}
