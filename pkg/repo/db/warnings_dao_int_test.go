package db

import (
	"fmt"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modinv"
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modwarn"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_GetAllWarnings() {
	// given
	expected := modwarn.AllMockWarnings(s.T())
	s.SetupLocations()
	s.SetupWarnings(expected...)

	expected[0].Number = "W001"
	expected[1].Number = "W002"

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetAllWarnings()

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_RetrieveWarning() {
	// given
	expected := modwarn.MockWarning1(s.T())
	s.SetupLocations()
	s.SetupWarnings(*expected)

	expected.Number = "W001"

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.RetrieveWarning(expected.Identity)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_GetWarningByCode() {
	// given
	expected := modwarn.MockWarning1(s.T())
	s.SetupLocations()
	s.SetupWarnings(*expected)

	expected.Number = "W001"

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.GetWarningForCode(expected.Code)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_FindWarnings() {
	// given
	expected := modwarn.AllMockWarnings(s.T())
	s.SetupLocations()
	s.SetupWarnings()
	expected[0].Number = "W001"
	expected[1].Number = "W002"

	dao := newDao(s.GetPostgresDb())

	searchCriteria := modwarn.WarningSearchCriteria{
		Locations:  []uuid.UUID{modinv.MockLocation1(s.T()).Identity},
		OrderBy:    modwarn.NewDefaultWarningSearchOrderBy(),
		Pagination: mod.NewDefaultSearchCriteriaPagination(),
	}

	// when
	result, err := dao.FindWarnings(searchCriteria)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_FindWarningsOffset() {
	// given
	expected := modwarn.AllMockWarnings(s.T())
	s.SetupLocations()
	s.SetupWarnings()
	expected[0].Number = "W001"
	expected[1].Number = "W002"

	dao := newDao(s.GetPostgresDb())

	searchCriteria := modwarn.WarningSearchCriteria{
		Locations:  []uuid.UUID{modinv.MockLocation1(s.T()).Identity},
		OrderBy:    modwarn.NewDefaultWarningSearchOrderBy(),
		Pagination: mod.NewDefaultSearchCriteriaPagination(),
	}

	// when
	result, err := dao.FindWarningsOffset(searchCriteria, expected[0].Identity)

	// then
	require.NoError(s.T(), err)
	assert.Equal(s.T(), 1, result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_FindWarnings_With_EventType() {
	// given
	expected := modwarn.AllMockWarnings(s.T())
	s.SetupLocations()
	s.SetupWarnings()
	expected[0].Number = "W001"
	expected[1].Number = "W002"

	dao := newDao(s.GetPostgresDb())

	searchCriteria := modwarn.WarningSearchCriteria{
		OrderBy:    modwarn.NewDefaultWarningSearchOrderBy(),
		Pagination: mod.NewDefaultSearchCriteriaPagination(),
		EventTypes: []string{"event type 1", "event type 2"},
	}

	// when
	result, err := dao.FindWarnings(searchCriteria)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), expected, result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_CreateWarning() {
	// given
	s.SetupLocations()

	expected := modwarn.MockWarning1(s.T())
	expected.Number = "W003"

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.CreateWarning(*expected)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)

	result, err = dao.RetrieveWarning(expected.Identity)

	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_GetNext10WarningNumberSequence() {
	// given
	dao := newDao(s.GetPostgresDb())
	err := dao.ResetWarningNumberSequence()
	require.NoError(s.T(), err)

	// when
	for i := 1; i <= 10; i++ {
		result, err := dao.GetNextWarningNumberSequence()
		require.NoError(s.T(), err)

		expected := fmt.Sprintf("W%03d", i)
		// then
		require.Equal(s.T(), expected, result, "unexpected warning number at iteration %d", i)
	}
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_GetNext10WarningNumberSequence_handle_1000() {
	// given
	dao := newDao(s.GetPostgresDb())
	err := dao.ResetWarningNumberSequence()
	require.NoError(s.T(), err)

	// when
	for i := 1; i <= 1000; i++ {
		result, err := dao.GetNextWarningNumberSequence()
		require.NoError(s.T(), err)

		expected := fmt.Sprintf("W%03d", i)
		// then
		require.Equal(s.T(), expected, result, "unexpected warning number at iteration %d", i)
	}
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_UpdateWarning() {
	// given
	expected := modwarn.MockWarning1(s.T())
	expected.Source = "updatedSource"

	s.SetupLocations()
	s.SetupWarnings(*modwarn.MockWarning1(s.T()))

	expected.Number = "W001"

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.UpdateWarning(*expected)

	expected.UpdatedAt = result.UpdatedAt

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)

	result, err = dao.RetrieveWarning(expected.Identity)

	expected.UpdatedAt = result.UpdatedAt

	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Equal(s.T(), *expected, *result)
}

func (s *DaoIntegrationTestSuite) TestIntWarningsDao_DeleteWarning() {
	// given
	expected := modwarn.MockWarning1(s.T())
	expected.WarningHistory = nil

	s.SetupLocations()
	s.SetupWarnings(*expected)

	dao := newDao(s.GetPostgresDb())

	// when
	result, err := dao.DeleteWarning(expected.Identity)

	// then
	require.NoError(s.T(), err)
	assert.NotNil(s.T(), result)
	assert.Truef(s.T(), result, "warning was not deleted")

	warning, err := dao.RetrieveWarning(expected.Identity)
	require.NoError(s.T(), err)
	assert.Nil(s.T(), warning)
}
