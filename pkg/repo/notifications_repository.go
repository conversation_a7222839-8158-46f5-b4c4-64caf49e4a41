package repo

import (
	"github.com/CyberOwlTeam/go-warning-and-scoring-api/pkg/mod/modnotif"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type NotificationsRepository interface {
	// warning notifications
	GetAllNotificationSettings() ([]modnotif.NotificationSetting, error)
	RetrieveNotificationSetting(identity uuid.UUID) (*modnotif.NotificationSetting, error)
	GetNotificationSettingByUserId(userId uuid.UUID) (*modnotif.NotificationSetting, error)
	CreateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)
	UpdateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error)
	DeleteNotificationSetting(identity uuid.UUID) (bool, error)

	//warning notification rules
	GetAllNotificationRules() ([]modnotif.NotificationRule, error)
	RetrieveNotificationRule(identity uuid.UUID) (*modnotif.NotificationRule, error)
	CreateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error)
	UpdateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error)
	DeleteNotificationRule(identity uuid.UUID) (bool, error)

	//warning affected items
	GetAllAffectedItems() ([]modnotif.AffectedItem, error)
	RetrieveAffectedItem(identity uuid.UUID) (*modnotif.AffectedItem, error)
	CreateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error)
	UpdateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error)
	DeleteAffectedItem(identity uuid.UUID) (bool, error)

	// notification queues
	GetAllNotificationQueues() ([]modnotif.NotificationQueue, error)
	RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error)
	GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error)
	CreateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)
	UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error)
	DeleteNotificationQueue(id modnotif.NotificationQueueIdentity) (bool, error)
}

func (r *repository) GetAllNotificationSettings() ([]modnotif.NotificationSetting, error) {
	return r.dao.GetAllNotificationSettings()
}

func (r *repository) RetrieveNotificationSetting(identity uuid.UUID) (*modnotif.NotificationSetting, error) {
	return r.dao.RetrieveNotificationSetting(identity)
}

func (r *repository) GetNotificationSettingByUserId(userId uuid.UUID) (*modnotif.NotificationSetting, error) {
	return r.dao.GetNotificationSettingByUserId(userId)
}

func (r *repository) CreateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	return r.dao.CreateNotificationSetting(setting)
}

func (r *repository) UpdateNotificationSetting(setting modnotif.NotificationSetting) (*modnotif.NotificationSetting, error) {
	return r.dao.UpdateNotificationSetting(setting)
}

func (r *repository) DeleteNotificationSetting(identity uuid.UUID) (bool, error) {
	var success bool
	err := r.dao.Transaction(func(tx *gorm.DB) error {
		var err error
		success, err = r.deleteNotificationSetting(identity, tx)
		return err
	})

	if err != nil {
		return false, err
	}

	return success, err
}

func (r *repository) GetAllNotificationRules() ([]modnotif.NotificationRule, error) {
	return r.dao.GetAllNotificationRules()
}

func (r *repository) RetrieveNotificationRule(identity uuid.UUID) (*modnotif.NotificationRule, error) {
	return r.dao.RetrieveNotificationRule(identity)
}

func (r *repository) CreateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	return r.dao.CreateNotificationRule(rule)
}

func (r *repository) UpdateNotificationRule(rule modnotif.NotificationRule) (*modnotif.NotificationRule, error) {
	return r.dao.UpdateNotificationRule(rule)
}

func (r *repository) DeleteNotificationRule(identity uuid.UUID) (bool, error) {
	var success bool
	err := r.dao.Transaction(func(tx *gorm.DB) error {
		var err error
		success, err = r.deleteNotificationRule(identity, tx)
		return err
	})

	if err != nil {
		return false, err
	}

	return success, err
}

func (r *repository) GetAllAffectedItems() ([]modnotif.AffectedItem, error) {
	return r.dao.GetAllAffectedItems()
}

func (r *repository) RetrieveAffectedItem(identity uuid.UUID) (*modnotif.AffectedItem, error) {
	return r.dao.RetrieveAffectedItem(identity)
}

func (r *repository) CreateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	return r.dao.CreateAffectedItem(item)
}

func (r *repository) UpdateAffectedItem(item modnotif.AffectedItem) (*modnotif.AffectedItem, error) {
	return r.dao.UpdateAffectedItem(item)
}

func (r *repository) DeleteAffectedItem(identity uuid.UUID) (bool, error) {
	return r.dao.DeleteAffectedItem(identity)
}

func (r *repository) deleteNotificationRule(identity uuid.UUID, tx *gorm.DB) (bool, error) {
	// delete entry from affected items notifs rules
	_, err := r.dao.DeleteAffectedItem4NotificationRule(identity, tx)
	if err != nil {
		return false, err
	}

	// finally, delete it from the notification rules table
	_, err = r.dao.DeleteNotificationRule(identity, tx)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (r *repository) deleteNotificationSetting(identity uuid.UUID, tx *gorm.DB) (bool, error) {
	// delete entry from notif rules
	_, err := r.dao.DeleteNotificationRule4NotificationSetting(identity, tx)
	if err != nil {
		return false, err
	}

	// delete from notif settings user
	_, err = r.dao.DeleteNotificationSettingsUserForNotificationSetting(identity, tx)
	if err != nil {
		return false, err
	}

	// finally, delete it from the settings table itself
	_, err = r.dao.DeleteNotificationSetting(identity, tx)
	if err != nil {
		return false, err
	}

	return true, nil

}

func (r *repository) GetAllNotificationQueues() ([]modnotif.NotificationQueue, error) {
	return r.dao.GetAllNotificationQueues()
}

func (r *repository) RetrieveNotificationQueue(id modnotif.NotificationQueueIdentity) (*modnotif.NotificationQueue, error) {
	return r.dao.RetrieveNotificationQueue(id)
}

func (r *repository) GetAllNotificationQueuesForUserAndFrequency(user uuid.UUID, frequency modnotif.NotificationFrequency) ([]modnotif.NotificationQueue, error) {
	return r.dao.GetAllNotificationQueuesForUserAndFrequency(user, frequency)
}

func (r *repository) CreateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error) {
	return r.dao.CreateNotificationQueue(notificationQueue)
}

func (r *repository) UpdateNotificationQueue(notificationQueue modnotif.NotificationQueue) (*modnotif.NotificationQueue, error) {
	return r.dao.UpdateNotificationQueue(notificationQueue)
}
func (r *repository) DeleteNotificationQueue(id modnotif.NotificationQueueIdentity) (bool, error) {
	return r.dao.DeleteNotificationQueue(id)
}
