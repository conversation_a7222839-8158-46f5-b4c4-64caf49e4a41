CREATE TABLE notification_queues
(
    user_id             UUID                   NOT NULL,
    notification_rule_id UUID                  NOT NULL,
    warning_id          UUID                   NOT NULL,
    frequency           notification_frequency NOT NULL,

    PRIMARY KEY (user_id, notification_rule_id, warning_id),

    CONSTRAINT fk_user_id
        FOREIGN KEY (user_id)
            REFERENCES users (id),

    CONSTRAINT fk_notification_rule_id
        FOREIGN KEY (notification_rule_id)
            REFERENCES notification_rules (id),

    CONSTRAINT fk_warning_id
        FOREIGN KEY (warning_id)
            REFERENCES warnings (id)
);