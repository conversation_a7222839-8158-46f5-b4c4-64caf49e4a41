-- add new cols
ALTER TABLE notification_settings2users
    ADD COLUMN is_additional_email_address BOOLEAN,
    ADD COLUMN additional_email_address    VARCHAR,
    ADD COLUMN group_notifications         BOOLEAN;

-- migrate any data
UPDATE notification_settings2users nsu
SET is_additional_email_address = ns.is_additional_email_address,
    additional_email_address    = ns.additional_email_address,
    group_notifications         = ns.group_notifications
FROM notification_settings ns
WHERE nsu.notification_setting_id = ns.id;

-- set defaults
ALTER TABLE notification_settings2users
    ALTER COLUMN is_additional_email_address SET NOT NULL;

ALTER TABLE notification_settings2users
    ALTER COLUMN group_notifications SET NOT NULL;

ALTER TABLE notification_settings2users
    ALTER COLUMN group_notifications SET DEFAULT TRUE;


-- drop cols
ALTER TABLE notification_settings
    DROP COLUMN is_additional_email_address,
    DROP COLUMN additional_email_address,
    DROP COLUMN group_notifications;